import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FinancingSchemesService } from './financing-schemes.service';
import { CreateFinancingSchemeDto, UpdateFinancingSchemeDto, FinancingSchemeResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('financing-schemes')
@Controller('financing-schemes')
export class FinancingSchemesController {
  constructor(private readonly financingSchemesService: FinancingSchemesService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new financing scheme (Admin only)' })
  @ApiResponse({ status: 201, description: 'Financing scheme created successfully', type: FinancingSchemeResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Financing scheme with name already exists' })
  async create(@Body() createFinancingSchemeDto: CreateFinancingSchemeDto, @Request() req: any) {
    return this.financingSchemesService.create(createFinancingSchemeDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all financing schemes' })
  @ApiResponse({ status: 200, description: 'Returns list of financing schemes', type: [FinancingSchemeResponseDto] })
  async findAll() {
    return this.financingSchemesService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get financing scheme by ID' })
  @ApiResponse({ status: 200, description: 'Returns financing scheme details', type: FinancingSchemeResponseDto })
  @ApiResponse({ status: 404, description: 'Financing scheme not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.financingSchemesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update financing scheme (Admin only)' })
  @ApiResponse({ status: 200, description: 'Financing scheme updated successfully', type: FinancingSchemeResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Financing scheme not found' })
  @ApiResponse({ status: 409, description: 'Financing scheme with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateFinancingSchemeDto: UpdateFinancingSchemeDto, @Request() req: any) {
    return this.financingSchemesService.update(id, updateFinancingSchemeDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete financing scheme (Admin only)' })
  @ApiResponse({ status: 200, description: 'Financing scheme deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Financing scheme not found' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.financingSchemesService.remove(id, req.user.sub);
  }
}
