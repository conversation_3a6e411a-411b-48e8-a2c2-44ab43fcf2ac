import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCareProvidersService } from './health-care-providers.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto, HealthCareProviderResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('health-care-providers')
@Controller('health-care-providers')
export class HealthCareProvidersController {
  constructor(private readonly healthCareProvidersService: HealthCareProvidersService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new health care provider (Admin only)' })
  @ApiResponse({ status: 201, description: 'Health care provider created successfully', type: HealthCareProviderResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Health care provider with name already exists' })
  async create(@Body() createHealthCareProviderDto: CreateHealthCareProviderDto, @Request() req: any) {
    return this.healthCareProvidersService.create(createHealthCareProviderDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all health care providers' })
  @ApiResponse({ status: 200, description: 'Returns list of health care providers', type: [HealthCareProviderResponseDto] })
  async findAll() {
    return this.healthCareProvidersService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get health care provider by ID' })
  @ApiResponse({ status: 200, description: 'Returns health care provider details', type: HealthCareProviderResponseDto })
  @ApiResponse({ status: 404, description: 'Health care provider not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.healthCareProvidersService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update health care provider (Admin only)' })
  @ApiResponse({ status: 200, description: 'Health care provider updated successfully', type: HealthCareProviderResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Health care provider not found' })
  @ApiResponse({ status: 409, description: 'Health care provider with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateHealthCareProviderDto: UpdateHealthCareProviderDto, @Request() req: any) {
    return this.healthCareProvidersService.update(id, updateHealthCareProviderDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete health care provider (Admin only)' })
  @ApiResponse({ status: 200, description: 'Health care provider deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Health care provider not found' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.healthCareProvidersService.remove(id, req.user.sub);
  }
}
