import api from "../api"

// Common interfaces for master data entities
export interface BaseEntity {
  id: number
  createdAt: string
  updatedAt: string
}

export interface BudgetType extends BaseEntity {
  typeName: string
}

export interface FundingSource extends BaseEntity {
  sourceName: string
}

export interface FundingUnit extends BaseEntity {
  unitName: string
}

export interface OrganizationType extends BaseEntity {
  typeName: string
}

export interface DomainIntervention extends BaseEntity {
  domainOfInterventionId: number
  domainName: string
  subDomainOfInterventionId: number
  subDomainName: string
}

export interface InputCategory extends BaseEntity {
  categoryName: string
}

export interface FinancingScheme extends BaseEntity {
  schemeName: string
}

export interface FinancingAgent extends BaseEntity {
  agentName: string
}

export interface HealthCareProvider extends BaseEntity {
  providerName: string
}

export interface Currency extends BaseEntity {
  currencyCode: string
  currencyName: string
}

// Request interfaces
export interface CreateBudgetTypeRequest {
  typeName: string
}

export interface CreateFundingSourceRequest {
  sourceName: string
}

export interface CreateFundingUnitRequest {
  unitName: string
}

export interface CreateOrganizationTypeRequest {
  typeName: string
}

export interface CreateDomainInterventionRequest {
  domainOfInterventionId: number
  domainName: string
  subDomainOfInterventionId: number
  subDomainName: string
}

export interface CreateInputCategoryRequest {
  categoryName: string
}

export interface CreateFinancingSchemeRequest {
  schemeName: string
}

export interface CreateFinancingAgentRequest {
  agentName: string
}

export interface CreateHealthCareProviderRequest {
  providerName: string
}

export interface CreateCurrencyRequest {
  currencyCode: string
  currencyName: string
}

export const masterDataService = {
  // Budget Types
  async getBudgetTypes(): Promise<BudgetType[]> {
    const response = await api.get("/budget-types")
    return response.data
  },

  async createBudgetType(data: CreateBudgetTypeRequest): Promise<BudgetType> {
    const response = await api.post("/budget-types", data)
    return response.data
  },

  async updateBudgetType(id: number, data: CreateBudgetTypeRequest): Promise<BudgetType> {
    const response = await api.patch(`/budget-types/${id}`, data)
    return response.data
  },

  async deleteBudgetType(id: number): Promise<void> {
    await api.delete(`/budget-types/${id}`)
  },

  // Funding Sources
  async getFundingSources(): Promise<FundingSource[]> {
    const response = await api.get("/funding-sources")
    return response.data
  },

  async createFundingSource(data: CreateFundingSourceRequest): Promise<FundingSource> {
    const response = await api.post("/funding-sources", data)
    return response.data
  },

  async updateFundingSource(id: number, data: CreateFundingSourceRequest): Promise<FundingSource> {
    const response = await api.patch(`/funding-sources/${id}`, data)
    return response.data
  },

  async deleteFundingSource(id: number): Promise<void> {
    await api.delete(`/funding-sources/${id}`)
  },

  // Funding Units
  async getFundingUnits(): Promise<FundingUnit[]> {
    const response = await api.get("/funding-units")
    return response.data
  },

  async createFundingUnit(data: CreateFundingUnitRequest): Promise<FundingUnit> {
    const response = await api.post("/funding-units", data)
    return response.data
  },

  async updateFundingUnit(id: number, data: CreateFundingUnitRequest): Promise<FundingUnit> {
    const response = await api.patch(`/funding-units/${id}`, data)
    return response.data
  },

  async deleteFundingUnit(id: number): Promise<void> {
    await api.delete(`/funding-units/${id}`)
  },

  // Organization Types
  async getOrganizationTypes(): Promise<OrganizationType[]> {
    const response = await api.get("/organization-types")
    return response.data
  },

  async createOrganizationType(data: CreateOrganizationTypeRequest): Promise<OrganizationType> {
    const response = await api.post("/organization-types", data)
    return response.data
  },

  async updateOrganizationType(id: number, data: CreateOrganizationTypeRequest): Promise<OrganizationType> {
    const response = await api.patch(`/organization-types/${id}`, data)
    return response.data
  },

  async deleteOrganizationType(id: number): Promise<void> {
    await api.delete(`/organization-types/${id}`)
  },
}
