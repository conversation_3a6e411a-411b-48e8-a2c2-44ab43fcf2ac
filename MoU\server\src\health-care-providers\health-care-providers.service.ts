import { Injectable, ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto } from './dto';

@Injectable()
export class HealthCareProvidersService {
  constructor(private prisma: PrismaService) {}

  async create(createHealthCareProviderDto: CreateHealthCareProviderDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can create health care providers');
    }

    // Check if provider with same name already exists
    const existingProvider = await this.prisma.healthCareProvider.findUnique({
      where: { providerName: createHealthCareProviderDto.providerName },
    });

    if (existingProvider) {
      throw new ConflictException('Health care provider with this name already exists');
    }

    return this.prisma.healthCareProvider.create({
      data: createHealthCareProviderDto,
    });
  }

  async findAll() {
    return this.prisma.healthCareProvider.findMany({
      where: { deleted: false },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: number) {
    const provider = await this.prisma.healthCareProvider.findUnique({
      where: { id, deleted: false },
    });

    if (!provider) {
      throw new NotFoundException('Health care provider not found');
    }

    return provider;
  }

  async update(id: number, updateHealthCareProviderDto: UpdateHealthCareProviderDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can update health care providers');
    }

    // Check if provider exists
    const existingProvider = await this.prisma.healthCareProvider.findUnique({
      where: { id, deleted: false },
    });

    if (!existingProvider) {
      throw new NotFoundException('Health care provider not found');
    }

    // Check if new name conflicts with existing provider (if name is being updated)
    if (updateHealthCareProviderDto.providerName && updateHealthCareProviderDto.providerName !== existingProvider.providerName) {
      const conflictingProvider = await this.prisma.healthCareProvider.findUnique({
        where: { providerName: updateHealthCareProviderDto.providerName },
      });

      if (conflictingProvider) {
        throw new ConflictException('Health care provider with this name already exists');
      }
    }

    return this.prisma.healthCareProvider.update({
      where: { id },
      data: updateHealthCareProviderDto,
    });
  }

  async remove(id: number, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can delete health care providers');
    }

    // Check if provider exists
    const existingProvider = await this.prisma.healthCareProvider.findUnique({
      where: { id, deleted: false },
    });

    if (!existingProvider) {
      throw new NotFoundException('Health care provider not found');
    }

    // Soft delete
    return this.prisma.healthCareProvider.update({
      where: { id },
      data: { deleted: true },
    });
  }
}
