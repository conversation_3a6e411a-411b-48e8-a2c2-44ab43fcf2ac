import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FinancingAgentsService } from './financing-agents.service';
import { CreateFinancingAgentDto, UpdateFinancingAgentDto, FinancingAgentResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('financing-agents')
@Controller('financing-agents')
export class FinancingAgentsController {
  constructor(private readonly financingAgentsService: FinancingAgentsService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new financing agent (Admin only)' })
  @ApiResponse({ status: 201, description: 'Financing agent created successfully', type: FinancingAgentResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Financing agent with name already exists' })
  async create(@Body() createFinancingAgentDto: CreateFinancingAgentDto, @Request() req: any) {
    return this.financingAgentsService.create(createFinancingAgentDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all financing agents' })
  @ApiResponse({ status: 200, description: 'Returns list of financing agents', type: [FinancingAgentResponseDto] })
  async findAll() {
    return this.financingAgentsService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get financing agent by ID' })
  @ApiResponse({ status: 200, description: 'Returns financing agent details', type: FinancingAgentResponseDto })
  @ApiResponse({ status: 404, description: 'Financing agent not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.financingAgentsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update financing agent (Admin only)' })
  @ApiResponse({ status: 200, description: 'Financing agent updated successfully', type: FinancingAgentResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Financing agent not found' })
  @ApiResponse({ status: 409, description: 'Financing agent with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateFinancingAgentDto: UpdateFinancingAgentDto, @Request() req: any) {
    return this.financingAgentsService.update(id, updateFinancingAgentDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete financing agent (Admin only)' })
  @ApiResponse({ status: 200, description: 'Financing agent deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Financing agent not found' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.financingAgentsService.remove(id, req.user.sub);
  }
}
