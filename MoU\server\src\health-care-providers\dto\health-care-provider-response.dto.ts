import { ApiProperty } from '@nestjs/swagger';

export class HealthCareProviderResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the health care provider',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Name of the health care provider',
    example: 'Rwanda Biomedical Center',
  })
  providerName: string;

  @ApiProperty({
    description: 'Description of the health care provider',
    example: 'National reference laboratory and biomedical research institution',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Location of the health care provider',
    example: 'Kigali, Rwanda',
    required: false,
  })
  location?: string;

  @ApiProperty({
    description: 'Contact email of the health care provider',
    example: '<EMAIL>',
    required: false,
  })
  contactEmail?: string;

  @ApiProperty({
    description: 'Contact phone number of the health care provider',
    example: '+250788123456',
    required: false,
  })
  contactPhone?: string;

  @ApiProperty({
    description: 'Website URL of the health care provider',
    example: 'https://rbc.gov.rw',
    required: false,
  })
  website?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
