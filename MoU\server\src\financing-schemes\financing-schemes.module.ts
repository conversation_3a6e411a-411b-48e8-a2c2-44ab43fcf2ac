import { Module } from '@nestjs/common';
import { FinancingSchemesService } from './financing-schemes.service';
import { FinancingSchemesController } from './financing-schemes.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [FinancingSchemesController],
  providers: [FinancingSchemesService],
  exports: [FinancingSchemesService],
})
export class FinancingSchemesModule {}
