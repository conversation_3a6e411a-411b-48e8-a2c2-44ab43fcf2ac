import { ApiProperty } from '@nestjs/swagger';

export class FinancingSchemeResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the financing scheme',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Name of the financing scheme',
    example: 'Health Sector Development Grant',
  })
  schemeName: string;

  @ApiProperty({
    description: 'Description of the financing scheme',
    example: 'Grant program for health sector infrastructure development',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Terms and conditions of the financing scheme',
    example: 'Maximum grant amount: $1M, Duration: 3 years, Matching funds required',
    required: false,
  })
  terms?: string;

  @ApiProperty({
    description: 'Additional conditions for the financing scheme',
    example: 'Must demonstrate community impact and sustainability plan',
    required: false,
  })
  conditions?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
