"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/page",{

/***/ "(app-pages-browser)/./app/dashboard/admin/page.tsx":
/*!**************************************!*\
  !*** ./app/dashboard/admin/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/master-data.service */ \"(app-pages-browser)/./lib/services/master-data.service.ts\");\n/* harmony import */ var _components_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table */ \"(app-pages-browser)/./components/data-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for different entity types\n    const [budgetTypes, setBudgetTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fundingSources, setFundingSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fundingUnits, setFundingUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [organizationTypes, setOrganizationTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthCareProviders, setHealthCareProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [financingAgents, setFinancingAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [financingSchemes, setFinancingSchemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputCategories, setInputCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [domainInterventions, setDomainInterventions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if ((user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\") {\n                loadAllData();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user\n    ]);\n    const loadAllData = async ()=>{\n        setLoading(true);\n        try {\n            const [budgetTypesData, fundingSourcesData, fundingUnitsData, organizationTypesData, healthCareProvidersData, financingAgentsData, financingSchemesData, inputCategoriesData, domainInterventionsData] = await Promise.all([\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getBudgetTypes(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFundingSources(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFundingUnits(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getOrganizationTypes(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getHealthCareProviders(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFinancingAgents(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFinancingSchemes(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getInputCategories(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getDomainInterventions()\n            ]);\n            setBudgetTypes(budgetTypesData);\n            setFundingSources(fundingSourcesData);\n            setFundingUnits(fundingUnitsData);\n            setOrganizationTypes(organizationTypesData);\n            setHealthCareProviders(healthCareProvidersData);\n            setFinancingAgents(financingAgentsData);\n            setFinancingSchemes(financingSchemesData);\n            setInputCategories(inputCategoriesData);\n            setDomainInterventions(domainInterventionsData);\n        } catch (error) {\n            console.error(\"Failed to load admin data:\", error);\n            setError(\"Failed to load admin data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Column definitions for different entity types\n    const budgetTypeColumns = [\n        {\n            key: \"typeName\",\n            label: \"Type Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const fundingSourceColumns = [\n        {\n            key: \"sourceName\",\n            label: \"Source Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const fundingUnitColumns = [\n        {\n            key: \"unitName\",\n            label: \"Unit Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const organizationTypeColumns = [\n        {\n            key: \"typeName\",\n            label: \"Type Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const handleEdit = (item, type)=>{\n        // Navigate to specific entity page for editing\n        window.location.href = \"/dashboard/\".concat(type);\n    };\n    const handleDelete = async (item, type)=>{\n        if (confirm(\"Are you sure you want to delete this \".concat(type, \"?\"))) {\n            try {\n                switch(type){\n                    case \"budget-types\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteBudgetType(item.id);\n                        break;\n                    case \"funding-sources\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteFundingSource(item.id);\n                        break;\n                    case \"funding-units\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteFundingUnit(item.id);\n                        break;\n                    case \"organization-types\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteOrganizationType(item.id);\n                        break;\n                }\n                setSuccess(\"\".concat(type.replace(\"-\", \" \"), \" deleted successfully\"));\n                await loadAllData();\n            } catch (error) {\n                var _error_response_data, _error_response;\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete \".concat(type));\n            }\n        }\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) !== \"ADMIN\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-red-600\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: \"You don't have permission to access this page. Admin access required.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"Admin Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Manage master data and system configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"System Administration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                defaultValue: \"budget-types\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                        className: \"grid w-full grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"budget-types\",\n                                children: \"Budget Types\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"funding-sources\",\n                                children: \"Funding Sources\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"funding-units\",\n                                children: \"Funding Units\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"organization-types\",\n                                children: \"Organization Types\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"budget-types\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Budget Types\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage budget type categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/budget-types\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Budget Type\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: budgetTypes,\n                                        columns: budgetTypeColumns,\n                                        searchKey: \"typeName\",\n                                        onEdit: (item)=>handleEdit(item, \"budget-types\"),\n                                        onDelete: (item)=>handleDelete(item, \"budget-types\"),\n                                        searchPlaceholder: \"Search budget types...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"funding-sources\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Funding Sources\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage funding source categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/funding-source\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Funding Source\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: fundingSources,\n                                        columns: fundingSourceColumns,\n                                        searchKey: \"sourceName\",\n                                        onEdit: (item)=>handleEdit(item, \"funding-sources\"),\n                                        onDelete: (item)=>handleDelete(item, \"funding-sources\"),\n                                        searchPlaceholder: \"Search funding sources...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"funding-units\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Funding Units\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage funding unit categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/funding-units\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Funding Unit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: fundingUnits,\n                                        columns: fundingUnitColumns,\n                                        searchKey: \"unitName\",\n                                        onEdit: (item)=>handleEdit(item, \"funding-units\"),\n                                        onDelete: (item)=>handleDelete(item, \"funding-units\"),\n                                        searchPlaceholder: \"Search funding units...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"organization-types\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Organization Types\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage organization type categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/organization-types\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Organization Type\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: organizationTypes,\n                                        columns: organizationTypeColumns,\n                                        searchKey: \"typeName\",\n                                        onEdit: (item)=>handleEdit(item, \"organization-types\"),\n                                        onDelete: (item)=>handleDelete(item, \"organization-types\"),\n                                        searchPlaceholder: \"Search organization types...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"1hd1olhkS1hQ0GrPjO/8y91gq8g=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/admin/page.tsx\n"));

/***/ })

});