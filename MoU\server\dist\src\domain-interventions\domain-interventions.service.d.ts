import { PrismaService } from '../prisma/prisma.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';
export declare class DomainInterventionsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createDomainInterventionDto: CreateDomainInterventionDto, userId: string): Promise<any>;
    findAll(): Promise<any>;
    findTree(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, userId: string): Promise<any>;
    remove(id: number, userId: string): Promise<any>;
    private checkCircularReference;
}
