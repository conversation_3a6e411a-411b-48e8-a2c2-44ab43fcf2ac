import { ApiProperty } from '@nestjs/swagger';

export class InputCategoryResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the input category',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Name of the input category',
    example: 'Medical Equipment',
  })
  categoryName: string;

  @ApiProperty({
    description: 'Description of the input category',
    example: 'Category for all medical equipment and devices',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Parent category ID for nested structure',
    example: 1,
    required: false,
  })
  parentId?: number;

  @ApiProperty({
    description: 'Parent category details',
    required: false,
  })
  parent?: InputCategoryResponseDto;

  @ApiProperty({
    description: 'Child categories',
    type: [InputCategoryResponseDto],
    required: false,
  })
  children?: InputCategoryResponseDto[];

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
