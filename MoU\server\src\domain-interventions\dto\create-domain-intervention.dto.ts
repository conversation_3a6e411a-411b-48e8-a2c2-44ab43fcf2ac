import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsInt } from 'class-validator';

export class CreateDomainInterventionDto {
  @ApiProperty({
    description: 'Name of the domain intervention',
    example: 'Primary Health Care',
  })
  @IsString()
  @IsNotEmpty()
  domainName: string;

  @ApiProperty({
    description: 'Description of the domain intervention',
    example: 'Comprehensive primary health care services and interventions',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Parent domain ID for hierarchical structure',
    example: 1,
    required: false,
  })
  @IsInt()
  @IsOptional()
  parentId?: number;
}
