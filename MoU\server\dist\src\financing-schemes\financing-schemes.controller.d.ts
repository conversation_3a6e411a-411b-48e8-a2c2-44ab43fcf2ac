import { FinancingSchemesService } from './financing-schemes.service';
import { CreateFinancingSchemeDto, UpdateFinancingSchemeDto } from './dto';
export declare class FinancingSchemesController {
    private readonly financingSchemesService;
    constructor(financingSchemesService: FinancingSchemesService);
    create(createFinancingSchemeDto: CreateFinancingSchemeDto, req: any): Promise<any>;
    findAll(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateFinancingSchemeDto: UpdateFinancingSchemeDto, req: any): Promise<any>;
    remove(id: number, req: any): Promise<any>;
}
