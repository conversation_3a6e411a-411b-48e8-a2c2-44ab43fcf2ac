import { Injectable, ConflictException, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto } from './dto';

@Injectable()
export class InputCategoriesService {
  constructor(private prisma: PrismaService) {}

  async create(createInputCategoryDto: CreateInputCategoryDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can create input categories');
    }

    // Check if category with same name already exists
    const existingCategory = await this.prisma.inputCategory.findUnique({
      where: { categoryName: createInputCategoryDto.categoryName },
    });

    if (existingCategory) {
      throw new ConflictException('Input category with this name already exists');
    }

    // If parentId is provided, check if parent exists
    if (createInputCategoryDto.parentId) {
      const parentCategory = await this.prisma.inputCategory.findUnique({
        where: { id: createInputCategoryDto.parentId, deleted: false },
      });

      if (!parentCategory) {
        throw new BadRequestException('Parent category not found');
      }
    }

    return this.prisma.inputCategory.create({
      data: createInputCategoryDto,
      include: {
        parent: true,
        children: true,
      },
    });
  }

  async findAll() {
    return this.prisma.inputCategory.findMany({
      where: { deleted: false },
      include: {
        parent: true,
        children: {
          where: { deleted: false },
          include: {
            children: {
              where: { deleted: false },
              include: {
                children: true, // Support deep nesting
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findTree() {
    // Get only root categories (no parent) with their full hierarchy
    return this.prisma.inputCategory.findMany({
      where: { 
        deleted: false,
        parentId: null,
      },
      include: {
        children: {
          where: { deleted: false },
          include: {
            children: {
              where: { deleted: false },
              include: {
                children: {
                  where: { deleted: false },
                  include: {
                    children: true, // Support unlimited nesting
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: number) {
    const category = await this.prisma.inputCategory.findUnique({
      where: { id, deleted: false },
      include: {
        parent: true,
        children: {
          where: { deleted: false },
          include: {
            children: {
              where: { deleted: false },
            },
          },
        },
      },
    });

    if (!category) {
      throw new NotFoundException('Input category not found');
    }

    return category;
  }

  async update(id: number, updateInputCategoryDto: UpdateInputCategoryDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can update input categories');
    }

    // Check if category exists
    const existingCategory = await this.prisma.inputCategory.findUnique({
      where: { id, deleted: false },
    });

    if (!existingCategory) {
      throw new NotFoundException('Input category not found');
    }

    // Check if new name conflicts with existing category (if name is being updated)
    if (updateInputCategoryDto.categoryName && updateInputCategoryDto.categoryName !== existingCategory.categoryName) {
      const conflictingCategory = await this.prisma.inputCategory.findUnique({
        where: { categoryName: updateInputCategoryDto.categoryName },
      });

      if (conflictingCategory) {
        throw new ConflictException('Input category with this name already exists');
      }
    }

    // If parentId is being updated, validate it
    if (updateInputCategoryDto.parentId !== undefined) {
      if (updateInputCategoryDto.parentId === id) {
        throw new BadRequestException('Category cannot be its own parent');
      }

      if (updateInputCategoryDto.parentId) {
        const parentCategory = await this.prisma.inputCategory.findUnique({
          where: { id: updateInputCategoryDto.parentId, deleted: false },
        });

        if (!parentCategory) {
          throw new BadRequestException('Parent category not found');
        }

        // Check for circular reference
        const isCircular = await this.checkCircularReference(id, updateInputCategoryDto.parentId);
        if (isCircular) {
          throw new BadRequestException('Circular reference detected');
        }
      }
    }

    return this.prisma.inputCategory.update({
      where: { id },
      data: updateInputCategoryDto,
      include: {
        parent: true,
        children: true,
      },
    });
  }

  async remove(id: number, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can delete input categories');
    }

    // Check if category exists
    const existingCategory = await this.prisma.inputCategory.findUnique({
      where: { id, deleted: false },
      include: {
        children: {
          where: { deleted: false },
        },
      },
    });

    if (!existingCategory) {
      throw new NotFoundException('Input category not found');
    }

    // Check if category has children
    if (existingCategory.children.length > 0) {
      throw new BadRequestException('Cannot delete category with child categories. Delete children first.');
    }

    // Soft delete
    return this.prisma.inputCategory.update({
      where: { id },
      data: { deleted: true },
    });
  }

  private async checkCircularReference(categoryId: number, parentId: number): Promise<boolean> {
    if (categoryId === parentId) {
      return true;
    }

    const parent = await this.prisma.inputCategory.findUnique({
      where: { id: parentId },
    });

    if (!parent || !parent.parentId) {
      return false;
    }

    return this.checkCircularReference(categoryId, parent.parentId);
  }
}
