import { InputCategoriesService } from './input-categories.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto } from './dto';
export declare class InputCategoriesController {
    private readonly inputCategoriesService;
    constructor(inputCategoriesService: InputCategoriesService);
    create(createInputCategoryDto: CreateInputCategoryDto, req: any): Promise<any>;
    findAll(): Promise<any>;
    findTree(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateInputCategoryDto: UpdateInputCategoryDto, req: any): Promise<any>;
    remove(id: number, req: any): Promise<any>;
}
