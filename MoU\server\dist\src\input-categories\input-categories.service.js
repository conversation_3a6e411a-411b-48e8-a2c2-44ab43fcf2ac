"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let InputCategoriesService = class InputCategoriesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createInputCategoryDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can create input categories');
        }
        const existingCategory = await this.prisma.inputCategory.findUnique({
            where: { categoryName: createInputCategoryDto.categoryName },
        });
        if (existingCategory) {
            throw new common_1.ConflictException('Input category with this name already exists');
        }
        if (createInputCategoryDto.parentId) {
            const parentCategory = await this.prisma.inputCategory.findUnique({
                where: { id: createInputCategoryDto.parentId, deleted: false },
            });
            if (!parentCategory) {
                throw new common_1.BadRequestException('Parent category not found');
            }
        }
        return this.prisma.inputCategory.create({
            data: createInputCategoryDto,
            include: {
                parent: true,
                children: true,
            },
        });
    }
    async findAll() {
        return this.prisma.inputCategory.findMany({
            where: { deleted: false },
            include: {
                parent: true,
                children: {
                    where: { deleted: false },
                    include: {
                        children: {
                            where: { deleted: false },
                            include: {
                                children: true,
                            },
                        },
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findTree() {
        return this.prisma.inputCategory.findMany({
            where: {
                deleted: false,
                parentId: null,
            },
            include: {
                children: {
                    where: { deleted: false },
                    include: {
                        children: {
                            where: { deleted: false },
                            include: {
                                children: {
                                    where: { deleted: false },
                                    include: {
                                        children: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOne(id) {
        const category = await this.prisma.inputCategory.findUnique({
            where: { id, deleted: false },
            include: {
                parent: true,
                children: {
                    where: { deleted: false },
                    include: {
                        children: {
                            where: { deleted: false },
                        },
                    },
                },
            },
        });
        if (!category) {
            throw new common_1.NotFoundException('Input category not found');
        }
        return category;
    }
    async update(id, updateInputCategoryDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can update input categories');
        }
        const existingCategory = await this.prisma.inputCategory.findUnique({
            where: { id, deleted: false },
        });
        if (!existingCategory) {
            throw new common_1.NotFoundException('Input category not found');
        }
        if (updateInputCategoryDto.categoryName && updateInputCategoryDto.categoryName !== existingCategory.categoryName) {
            const conflictingCategory = await this.prisma.inputCategory.findUnique({
                where: { categoryName: updateInputCategoryDto.categoryName },
            });
            if (conflictingCategory) {
                throw new common_1.ConflictException('Input category with this name already exists');
            }
        }
        if (updateInputCategoryDto.parentId !== undefined) {
            if (updateInputCategoryDto.parentId === id) {
                throw new common_1.BadRequestException('Category cannot be its own parent');
            }
            if (updateInputCategoryDto.parentId) {
                const parentCategory = await this.prisma.inputCategory.findUnique({
                    where: { id: updateInputCategoryDto.parentId, deleted: false },
                });
                if (!parentCategory) {
                    throw new common_1.BadRequestException('Parent category not found');
                }
                const isCircular = await this.checkCircularReference(id, updateInputCategoryDto.parentId);
                if (isCircular) {
                    throw new common_1.BadRequestException('Circular reference detected');
                }
            }
        }
        return this.prisma.inputCategory.update({
            where: { id },
            data: updateInputCategoryDto,
            include: {
                parent: true,
                children: true,
            },
        });
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can delete input categories');
        }
        const existingCategory = await this.prisma.inputCategory.findUnique({
            where: { id, deleted: false },
            include: {
                children: {
                    where: { deleted: false },
                },
            },
        });
        if (!existingCategory) {
            throw new common_1.NotFoundException('Input category not found');
        }
        if (existingCategory.children.length > 0) {
            throw new common_1.BadRequestException('Cannot delete category with child categories. Delete children first.');
        }
        return this.prisma.inputCategory.update({
            where: { id },
            data: { deleted: true },
        });
    }
    async checkCircularReference(categoryId, parentId) {
        if (categoryId === parentId) {
            return true;
        }
        const parent = await this.prisma.inputCategory.findUnique({
            where: { id: parentId },
        });
        if (!parent || !parent.parentId) {
            return false;
        }
        return this.checkCircularReference(categoryId, parent.parentId);
    }
};
exports.InputCategoriesService = InputCategoriesService;
exports.InputCategoriesService = InputCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], InputCategoriesService);
//# sourceMappingURL=input-categories.service.js.map