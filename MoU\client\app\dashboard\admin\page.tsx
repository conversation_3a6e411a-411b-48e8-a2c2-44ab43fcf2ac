"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, Settings, Database } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { masterDataService } from "@/lib/services/master-data.service"
import { DataTable } from "@/components/data-table"

export default function AdminPage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // State for different entity types
  const [budgetTypes, setBudgetTypes] = useState([])
  const [fundingSources, setFundingSources] = useState([])
  const [fundingUnits, setFundingUnits] = useState([])
  const [organizationTypes, setOrganizationTypes] = useState([])
  const [healthCareProviders, setHealthCareProviders] = useState([])
  const [financingAgents, setFinancingAgents] = useState([])
  const [financingSchemes, setFinancingSchemes] = useState([])
  const [inputCategories, setInputCategories] = useState([])
  const [domainInterventions, setDomainInterventions] = useState([])

  useEffect(() => {
    if (user?.role === "ADMIN") {
      loadAllData()
    }
  }, [user])

  const loadAllData = async () => {
    setLoading(true)
    try {
      const [
        budgetTypesData,
        fundingSourcesData,
        fundingUnitsData,
        organizationTypesData,
        healthCareProvidersData,
        financingAgentsData,
        financingSchemesData,
        inputCategoriesData,
        domainInterventionsData
      ] = await Promise.all([
        masterDataService.getBudgetTypes(),
        masterDataService.getFundingSources(),
        masterDataService.getFundingUnits(),
        masterDataService.getOrganizationTypes(),
        masterDataService.getHealthCareProviders(),
        masterDataService.getFinancingAgents(),
        masterDataService.getFinancingSchemes(),
        masterDataService.getInputCategories(),
        masterDataService.getDomainInterventions(),
      ])

      setBudgetTypes(budgetTypesData)
      setFundingSources(fundingSourcesData)
      setFundingUnits(fundingUnitsData)
      setOrganizationTypes(organizationTypesData)
      setHealthCareProviders(healthCareProvidersData)
      setFinancingAgents(financingAgentsData)
      setFinancingSchemes(financingSchemesData)
      setInputCategories(inputCategoriesData)
      setDomainInterventions(domainInterventionsData)
    } catch (error) {
      console.error("Failed to load admin data:", error)
      setError("Failed to load admin data")
    } finally {
      setLoading(false)
    }
  }

  // Column definitions for different entity types
  const budgetTypeColumns = [
    { key: "typeName", label: "Type Name" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
    { key: "updatedAt", label: "Updated At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const fundingSourceColumns = [
    { key: "sourceName", label: "Source Name" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
    { key: "updatedAt", label: "Updated At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const fundingUnitColumns = [
    { key: "unitName", label: "Unit Name" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
    { key: "updatedAt", label: "Updated At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const organizationTypeColumns = [
    { key: "typeName", label: "Type Name" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
    { key: "updatedAt", label: "Updated At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const healthCareProviderColumns = [
    { key: "providerName", label: "Provider Name" },
    { key: "location", label: "Location" },
    { key: "contactEmail", label: "Contact Email" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const financingAgentColumns = [
    { key: "agentName", label: "Agent Name" },
    { key: "description", label: "Description" },
    { key: "contactInfo", label: "Contact Info" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const financingSchemeColumns = [
    { key: "schemeName", label: "Scheme Name" },
    { key: "description", label: "Description" },
    { key: "terms", label: "Terms" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const inputCategoryColumns = [
    { key: "categoryName", label: "Category Name" },
    { key: "description", label: "Description" },
    { key: "parent", label: "Parent Category", render: (value: any) => value?.categoryName || "Root" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const domainInterventionColumns = [
    { key: "domainName", label: "Domain Name" },
    { key: "description", label: "Description" },
    { key: "parent", label: "Parent Domain", render: (value: any) => value?.domainName || "Root" },
    { key: "createdAt", label: "Created At", render: (value: string) => new Date(value).toLocaleDateString() },
  ]

  const handleEdit = (item: any, type: string) => {
    // Navigate to specific entity page for editing
    window.location.href = `/dashboard/${type}`
  }

  const handleDelete = async (item: any, type: string) => {
    if (confirm(`Are you sure you want to delete this ${type}?`)) {
      try {
        switch (type) {
          case "budget-types":
            await masterDataService.deleteBudgetType(item.id)
            break
          case "funding-sources":
            await masterDataService.deleteFundingSource(item.id)
            break
          case "funding-units":
            await masterDataService.deleteFundingUnit(item.id)
            break
          case "organization-types":
            await masterDataService.deleteOrganizationType(item.id)
            break
        }
        setSuccess(`${type.replace("-", " ")} deleted successfully`)
        await loadAllData()
      } catch (error: any) {
        setError(error.response?.data?.message || `Failed to delete ${type}`)
      }
    }
  }

  if (user?.role !== "ADMIN") {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-red-600">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page. Admin access required.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Admin Dashboard</h2>
          <p className="text-muted-foreground">Manage master data and system configuration</p>
        </div>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">System Administration</span>
        </div>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Tabs defaultValue="budget-types" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="budget-types">Budget Types</TabsTrigger>
            <TabsTrigger value="funding-sources">Funding Sources</TabsTrigger>
            <TabsTrigger value="funding-units">Funding Units</TabsTrigger>
            <TabsTrigger value="organization-types">Organization Types</TabsTrigger>
          </TabsList>

          <TabsContent value="budget-types" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Budget Types
                  </CardTitle>
                  <CardDescription>Manage budget type categories</CardDescription>
                </div>
                <Button onClick={() => window.location.href = "/dashboard/budget-types"}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Budget Type
                </Button>
              </CardHeader>
              <CardContent>
                <DataTable
                  data={budgetTypes}
                  columns={budgetTypeColumns}
                  searchKey="typeName"
                  onEdit={(item) => handleEdit(item, "budget-types")}
                  onDelete={(item) => handleDelete(item, "budget-types")}
                  searchPlaceholder="Search budget types..."
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="funding-sources" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Funding Sources
                  </CardTitle>
                  <CardDescription>Manage funding source categories</CardDescription>
                </div>
                <Button onClick={() => window.location.href = "/dashboard/funding-source"}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Funding Source
                </Button>
              </CardHeader>
              <CardContent>
                <DataTable
                  data={fundingSources}
                  columns={fundingSourceColumns}
                  searchKey="sourceName"
                  onEdit={(item) => handleEdit(item, "funding-sources")}
                  onDelete={(item) => handleDelete(item, "funding-sources")}
                  searchPlaceholder="Search funding sources..."
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="funding-units" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Funding Units
                  </CardTitle>
                  <CardDescription>Manage funding unit categories</CardDescription>
                </div>
                <Button onClick={() => window.location.href = "/dashboard/funding-units"}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Funding Unit
                </Button>
              </CardHeader>
              <CardContent>
                <DataTable
                  data={fundingUnits}
                  columns={fundingUnitColumns}
                  searchKey="unitName"
                  onEdit={(item) => handleEdit(item, "funding-units")}
                  onDelete={(item) => handleDelete(item, "funding-units")}
                  searchPlaceholder="Search funding units..."
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="organization-types" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Organization Types
                  </CardTitle>
                  <CardDescription>Manage organization type categories</CardDescription>
                </div>
                <Button onClick={() => window.location.href = "/dashboard/organization-types"}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Organization Type
                </Button>
              </CardHeader>
              <CardContent>
                <DataTable
                  data={organizationTypes}
                  columns={organizationTypeColumns}
                  searchKey="typeName"
                  onEdit={(item) => handleEdit(item, "organization-types")}
                  onDelete={(item) => handleDelete(item, "organization-types")}
                  searchPlaceholder="Search organization types..."
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
