"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancingSchemesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const financing_schemes_service_1 = require("./financing-schemes.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let FinancingSchemesController = class FinancingSchemesController {
    constructor(financingSchemesService) {
        this.financingSchemesService = financingSchemesService;
    }
    async create(createFinancingSchemeDto, req) {
        return this.financingSchemesService.create(createFinancingSchemeDto, req.user.sub);
    }
    async findAll() {
        return this.financingSchemesService.findAll();
    }
    async findOne(id) {
        return this.financingSchemesService.findOne(id);
    }
    async update(id, updateFinancingSchemeDto, req) {
        return this.financingSchemesService.update(id, updateFinancingSchemeDto, req.user.sub);
    }
    async remove(id, req) {
        return this.financingSchemesService.remove(id, req.user.sub);
    }
};
exports.FinancingSchemesController = FinancingSchemesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new financing scheme (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Financing scheme created successfully', type: dto_1.FinancingSchemeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Financing scheme with name already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateFinancingSchemeDto, Object]),
    __metadata("design:returntype", Promise)
], FinancingSchemesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all financing schemes' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of financing schemes', type: [dto_1.FinancingSchemeResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FinancingSchemesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get financing scheme by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns financing scheme details', type: dto_1.FinancingSchemeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Financing scheme not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], FinancingSchemesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update financing scheme (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Financing scheme updated successfully', type: dto_1.FinancingSchemeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Financing scheme not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Financing scheme with name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateFinancingSchemeDto, Object]),
    __metadata("design:returntype", Promise)
], FinancingSchemesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete financing scheme (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Financing scheme deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Financing scheme not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinancingSchemesController.prototype, "remove", null);
exports.FinancingSchemesController = FinancingSchemesController = __decorate([
    (0, swagger_1.ApiTags)('financing-schemes'),
    (0, common_1.Controller)('financing-schemes'),
    __metadata("design:paramtypes", [financing_schemes_service_1.FinancingSchemesService])
], FinancingSchemesController);
//# sourceMappingURL=financing-schemes.controller.js.map