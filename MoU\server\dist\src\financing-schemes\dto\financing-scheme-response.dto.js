"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancingSchemeResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class FinancingSchemeResponseDto {
}
exports.FinancingSchemeResponseDto = FinancingSchemeResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the financing scheme',
        example: 1,
    }),
    __metadata("design:type", Number)
], FinancingSchemeResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the financing scheme',
        example: 'Health Sector Development Grant',
    }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "schemeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the financing scheme',
        example: 'Grant program for health sector infrastructure development',
        required: false,
    }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Terms and conditions of the financing scheme',
        example: 'Maximum grant amount: $1M, Duration: 3 years, Matching funds required',
        required: false,
    }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "terms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional conditions for the financing scheme',
        example: 'Must demonstrate community impact and sustainability plan',
        required: false,
    }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "conditions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], FinancingSchemeResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], FinancingSchemeResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=financing-scheme-response.dto.js.map