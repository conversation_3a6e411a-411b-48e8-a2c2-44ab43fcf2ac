"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCareProvidersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const health_care_providers_service_1 = require("./health-care-providers.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let HealthCareProvidersController = class HealthCareProvidersController {
    constructor(healthCareProvidersService) {
        this.healthCareProvidersService = healthCareProvidersService;
    }
    async create(createHealthCareProviderDto, req) {
        return this.healthCareProvidersService.create(createHealthCareProviderDto, req.user.sub);
    }
    async findAll() {
        return this.healthCareProvidersService.findAll();
    }
    async findOne(id) {
        return this.healthCareProvidersService.findOne(id);
    }
    async update(id, updateHealthCareProviderDto, req) {
        return this.healthCareProvidersService.update(id, updateHealthCareProviderDto, req.user.sub);
    }
    async remove(id, req) {
        return this.healthCareProvidersService.remove(id, req.user.sub);
    }
};
exports.HealthCareProvidersController = HealthCareProvidersController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new health care provider (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Health care provider created successfully', type: dto_1.HealthCareProviderResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Health care provider with name already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateHealthCareProviderDto, Object]),
    __metadata("design:returntype", Promise)
], HealthCareProvidersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all health care providers' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of health care providers', type: [dto_1.HealthCareProviderResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthCareProvidersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get health care provider by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns health care provider details', type: dto_1.HealthCareProviderResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Health care provider not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], HealthCareProvidersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update health care provider (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Health care provider updated successfully', type: dto_1.HealthCareProviderResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Health care provider not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Health care provider with name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateHealthCareProviderDto, Object]),
    __metadata("design:returntype", Promise)
], HealthCareProvidersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete health care provider (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Health care provider deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Health care provider not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], HealthCareProvidersController.prototype, "remove", null);
exports.HealthCareProvidersController = HealthCareProvidersController = __decorate([
    (0, swagger_1.ApiTags)('health-care-providers'),
    (0, common_1.Controller)('health-care-providers'),
    __metadata("design:paramtypes", [health_care_providers_service_1.HealthCareProvidersService])
], HealthCareProvidersController);
//# sourceMappingURL=health-care-providers.controller.js.map