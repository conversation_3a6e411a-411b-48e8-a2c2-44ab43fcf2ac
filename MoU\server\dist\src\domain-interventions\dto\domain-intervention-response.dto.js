"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainInterventionResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class DomainInterventionResponseDto {
}
exports.DomainInterventionResponseDto = DomainInterventionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the domain intervention',
        example: 1,
    }),
    __metadata("design:type", Number)
], DomainInterventionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the domain intervention',
        example: 'Primary Health Care',
    }),
    __metadata("design:type", String)
], DomainInterventionResponseDto.prototype, "domainName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the domain intervention',
        example: 'Comprehensive primary health care services and interventions',
        required: false,
    }),
    __metadata("design:type", String)
], DomainInterventionResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent domain ID for hierarchical structure',
        example: 1,
        required: false,
    }),
    __metadata("design:type", Number)
], DomainInterventionResponseDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent domain details',
        required: false,
    }),
    __metadata("design:type", DomainInterventionResponseDto)
], DomainInterventionResponseDto.prototype, "parent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Child domains (sub-domains)',
        type: [DomainInterventionResponseDto],
        required: false,
    }),
    __metadata("design:type", Array)
], DomainInterventionResponseDto.prototype, "children", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], DomainInterventionResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], DomainInterventionResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=domain-intervention-response.dto.js.map