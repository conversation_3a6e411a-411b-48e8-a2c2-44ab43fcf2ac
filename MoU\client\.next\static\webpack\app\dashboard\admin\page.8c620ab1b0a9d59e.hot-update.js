"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/page",{

/***/ "(app-pages-browser)/./app/dashboard/admin/page.tsx":
/*!**************************************!*\
  !*** ./app/dashboard/admin/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Loader2,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/master-data.service */ \"(app-pages-browser)/./lib/services/master-data.service.ts\");\n/* harmony import */ var _components_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table */ \"(app-pages-browser)/./components/data-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for different entity types\n    const [budgetTypes, setBudgetTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fundingSources, setFundingSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fundingUnits, setFundingUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [organizationTypes, setOrganizationTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthCareProviders, setHealthCareProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [financingAgents, setFinancingAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [financingSchemes, setFinancingSchemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputCategories, setInputCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [domainInterventions, setDomainInterventions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if ((user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\") {\n                loadAllData();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user\n    ]);\n    const loadAllData = async ()=>{\n        setLoading(true);\n        try {\n            const [budgetTypesData, fundingSourcesData, fundingUnitsData, organizationTypesData, healthCareProvidersData, financingAgentsData, financingSchemesData, inputCategoriesData, domainInterventionsData] = await Promise.all([\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getBudgetTypes(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFundingSources(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFundingUnits(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getOrganizationTypes(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getHealthCareProviders(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFinancingAgents(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getFinancingSchemes(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getInputCategories(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.getDomainInterventions()\n            ]);\n            setBudgetTypes(budgetTypesData);\n            setFundingSources(fundingSourcesData);\n            setFundingUnits(fundingUnitsData);\n            setOrganizationTypes(organizationTypesData);\n            setHealthCareProviders(healthCareProvidersData);\n            setFinancingAgents(financingAgentsData);\n            setFinancingSchemes(financingSchemesData);\n            setInputCategories(inputCategoriesData);\n            setDomainInterventions(domainInterventionsData);\n        } catch (error) {\n            console.error(\"Failed to load admin data:\", error);\n            setError(\"Failed to load admin data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Column definitions for different entity types\n    const budgetTypeColumns = [\n        {\n            key: \"typeName\",\n            label: \"Type Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const fundingSourceColumns = [\n        {\n            key: \"sourceName\",\n            label: \"Source Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const fundingUnitColumns = [\n        {\n            key: \"unitName\",\n            label: \"Unit Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const organizationTypeColumns = [\n        {\n            key: \"typeName\",\n            label: \"Type Name\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        },\n        {\n            key: \"updatedAt\",\n            label: \"Updated At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const healthCareProviderColumns = [\n        {\n            key: \"providerName\",\n            label: \"Provider Name\"\n        },\n        {\n            key: \"location\",\n            label: \"Location\"\n        },\n        {\n            key: \"contactEmail\",\n            label: \"Contact Email\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const financingAgentColumns = [\n        {\n            key: \"agentName\",\n            label: \"Agent Name\"\n        },\n        {\n            key: \"description\",\n            label: \"Description\"\n        },\n        {\n            key: \"contactInfo\",\n            label: \"Contact Info\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const financingSchemeColumns = [\n        {\n            key: \"schemeName\",\n            label: \"Scheme Name\"\n        },\n        {\n            key: \"description\",\n            label: \"Description\"\n        },\n        {\n            key: \"terms\",\n            label: \"Terms\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const inputCategoryColumns = [\n        {\n            key: \"categoryName\",\n            label: \"Category Name\"\n        },\n        {\n            key: \"description\",\n            label: \"Description\"\n        },\n        {\n            key: \"parent\",\n            label: \"Parent Category\",\n            render: (value)=>(value === null || value === void 0 ? void 0 : value.categoryName) || \"Root\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const domainInterventionColumns = [\n        {\n            key: \"domainName\",\n            label: \"Domain Name\"\n        },\n        {\n            key: \"description\",\n            label: \"Description\"\n        },\n        {\n            key: \"parent\",\n            label: \"Parent Domain\",\n            render: (value)=>(value === null || value === void 0 ? void 0 : value.domainName) || \"Root\"\n        },\n        {\n            key: \"createdAt\",\n            label: \"Created At\",\n            render: (value)=>new Date(value).toLocaleDateString()\n        }\n    ];\n    const handleEdit = (item, type)=>{\n        // Navigate to specific entity page for editing\n        window.location.href = \"/dashboard/\".concat(type);\n    };\n    const handleDelete = async (item, type)=>{\n        if (confirm(\"Are you sure you want to delete this \".concat(type, \"?\"))) {\n            try {\n                switch(type){\n                    case \"budget-types\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteBudgetType(item.id);\n                        break;\n                    case \"funding-sources\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteFundingSource(item.id);\n                        break;\n                    case \"funding-units\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteFundingUnit(item.id);\n                        break;\n                    case \"organization-types\":\n                        await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_7__.masterDataService.deleteOrganizationType(item.id);\n                        break;\n                }\n                setSuccess(\"\".concat(type.replace(\"-\", \" \"), \" deleted successfully\"));\n                await loadAllData();\n            } catch (error) {\n                var _error_response_data, _error_response;\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete \".concat(type));\n            }\n        }\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) !== \"ADMIN\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-red-600\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: \"You don't have permission to access this page. Admin access required.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"Admin Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Manage master data and system configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"System Administration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                defaultValue: \"budget-types\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                        className: \"grid w-full grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"budget-types\",\n                                children: \"Budget Types\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"funding-sources\",\n                                children: \"Funding Sources\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"funding-units\",\n                                children: \"Funding Units\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"organization-types\",\n                                children: \"Organization Types\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"budget-types\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Budget Types\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage budget type categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/budget-types\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Budget Type\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: budgetTypes,\n                                        columns: budgetTypeColumns,\n                                        searchKey: \"typeName\",\n                                        onEdit: (item)=>handleEdit(item, \"budget-types\"),\n                                        onDelete: (item)=>handleDelete(item, \"budget-types\"),\n                                        searchPlaceholder: \"Search budget types...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"funding-sources\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Funding Sources\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage funding source categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/funding-source\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Funding Source\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: fundingSources,\n                                        columns: fundingSourceColumns,\n                                        searchKey: \"sourceName\",\n                                        onEdit: (item)=>handleEdit(item, \"funding-sources\"),\n                                        onDelete: (item)=>handleDelete(item, \"funding-sources\"),\n                                        searchPlaceholder: \"Search funding sources...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"funding-units\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Funding Units\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage funding unit categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/funding-units\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Funding Unit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: fundingUnits,\n                                        columns: fundingUnitColumns,\n                                        searchKey: \"unitName\",\n                                        onEdit: (item)=>handleEdit(item, \"funding-units\"),\n                                        onDelete: (item)=>handleDelete(item, \"funding-units\"),\n                                        searchPlaceholder: \"Search funding units...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"organization-types\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Organization Types\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Manage organization type categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.location.href = \"/dashboard/organization-types\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Loader2_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Organization Type\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                                        data: organizationTypes,\n                                        columns: organizationTypeColumns,\n                                        searchKey: \"typeName\",\n                                        onEdit: (item)=>handleEdit(item, \"organization-types\"),\n                                        onDelete: (item)=>handleDelete(item, \"organization-types\"),\n                                        searchPlaceholder: \"Search organization types...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\admin\\\\page.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"1hd1olhkS1hQ0GrPjO/8y91gq8g=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/admin/page.tsx\n"));

/***/ })

});