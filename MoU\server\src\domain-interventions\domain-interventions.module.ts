import { Module } from '@nestjs/common';
import { DomainInterventionsService } from './domain-interventions.service';
import { DomainInterventionsController } from './domain-interventions.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [DomainInterventionsController],
  providers: [DomainInterventionsService],
  exports: [DomainInterventionsService],
})
export class DomainInterventionsModule {}
