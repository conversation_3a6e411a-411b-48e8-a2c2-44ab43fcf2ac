"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputCategoriesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const input_categories_service_1 = require("./input-categories.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let InputCategoriesController = class InputCategoriesController {
    constructor(inputCategoriesService) {
        this.inputCategoriesService = inputCategoriesService;
    }
    async create(createInputCategoryDto, req) {
        return this.inputCategoriesService.create(createInputCategoryDto, req.user.sub);
    }
    async findAll() {
        return this.inputCategoriesService.findAll();
    }
    async findTree() {
        return this.inputCategoriesService.findTree();
    }
    async findOne(id) {
        return this.inputCategoriesService.findOne(id);
    }
    async update(id, updateInputCategoryDto, req) {
        return this.inputCategoriesService.update(id, updateInputCategoryDto, req.user.sub);
    }
    async remove(id, req) {
        return this.inputCategoriesService.remove(id, req.user.sub);
    }
};
exports.InputCategoriesController = InputCategoriesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new input category (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Input category created successfully', type: dto_1.InputCategoryResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid parent category' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Input category with name already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateInputCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], InputCategoriesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all input categories with hierarchy' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of input categories', type: [dto_1.InputCategoryResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], InputCategoriesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('tree'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get input categories as tree structure (root categories with children)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns tree structure of input categories', type: [dto_1.InputCategoryResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], InputCategoriesController.prototype, "findTree", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get input category by ID with hierarchy' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns input category details', type: dto_1.InputCategoryResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input category not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InputCategoriesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update input category (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Input category updated successfully', type: dto_1.InputCategoryResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid parent category or circular reference' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input category not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Input category with name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateInputCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], InputCategoriesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete input category (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Input category deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Category has children' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input category not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], InputCategoriesController.prototype, "remove", null);
exports.InputCategoriesController = InputCategoriesController = __decorate([
    (0, swagger_1.ApiTags)('input-categories'),
    (0, common_1.Controller)('input-categories'),
    __metadata("design:paramtypes", [input_categories_service_1.InputCategoriesService])
], InputCategoriesController);
//# sourceMappingURL=input-categories.controller.js.map