"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputCategoryResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class InputCategoryResponseDto {
}
exports.InputCategoryResponseDto = InputCategoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the input category',
        example: 1,
    }),
    __metadata("design:type", Number)
], InputCategoryResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the input category',
        example: 'Medical Equipment',
    }),
    __metadata("design:type", String)
], InputCategoryResponseDto.prototype, "categoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the input category',
        example: 'Category for all medical equipment and devices',
        required: false,
    }),
    __metadata("design:type", String)
], InputCategoryResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent category ID for nested structure',
        example: 1,
        required: false,
    }),
    __metadata("design:type", Number)
], InputCategoryResponseDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent category details',
        required: false,
    }),
    __metadata("design:type", InputCategoryResponseDto)
], InputCategoryResponseDto.prototype, "parent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Child categories',
        type: [InputCategoryResponseDto],
        required: false,
    }),
    __metadata("design:type", Array)
], InputCategoryResponseDto.prototype, "children", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], InputCategoryResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], InputCategoryResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=input-category-response.dto.js.map