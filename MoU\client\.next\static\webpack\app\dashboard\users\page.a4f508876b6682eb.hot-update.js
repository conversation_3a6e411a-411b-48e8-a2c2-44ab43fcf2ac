"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/users/page",{

/***/ "(app-pages-browser)/./app/dashboard/users/page.tsx":
/*!**************************************!*\
  !*** ./app/dashboard/users/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UsersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_services_users_service__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/users.service */ \"(app-pages-browser)/./lib/services/users.service.ts\");\n/* harmony import */ var _lib_services_organization_service__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/organization.service */ \"(app-pages-browser)/./lib/services/organization.service.ts\");\n/* harmony import */ var _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/auth.service */ \"(app-pages-browser)/./lib/services/auth.service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst USER_ROLES = [\n    {\n        value: \"ADMIN\",\n        label: \"Admin\"\n    },\n    {\n        value: \"COORDINATOR\",\n        label: \"Coordinator\"\n    },\n    {\n        value: \"LEGAL\",\n        label: \"Legal\"\n    },\n    {\n        value: \"TECHNICAL_EXPERT\",\n        label: \"Technical Expert\"\n    },\n    {\n        value: \"HOD\",\n        label: \"Head of Department\"\n    },\n    {\n        value: \"PS\",\n        label: \"Permanent Secretary\"\n    },\n    {\n        value: \"MINISTER\",\n        label: \"Minister\"\n    }\n];\nfunction UsersPage() {\n    _s();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [formLoading, setFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form state\n    const [firstName, setFirstName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastName, setLastName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UsersPage.useEffect\"], []);\n    const loadUsers = async ()=>{\n        try {\n            setLoading(true);\n            const data = await _lib_services_users_service__WEBPACK_IMPORTED_MODULE_11__.usersService.getUsers();\n            setUsers(data);\n        } catch (error) {\n            console.error(\"Failed to load users:\", error);\n            setError(\"Failed to load users\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOrganizations = async ()=>{\n        try {\n            const data = await _lib_services_organization_service__WEBPACK_IMPORTED_MODULE_12__.organizationService.getOrganizations();\n            setOrganizations(data);\n        } catch (error) {\n            console.error(\"Failed to load organizations:\", error);\n        }\n    };\n    const filteredUsers = users.filter((user)=>{\n        var _user_organization;\n        return user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) || user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase()) || user.role.toLowerCase().includes(searchTerm.toLowerCase()) || (((_user_organization = user.organization) === null || _user_organization === void 0 ? void 0 : _user_organization.organizationName) || \"\").toLowerCase().includes(searchTerm.toLowerCase());\n    });\n    const resetForm = ()=>{\n        setFirstName(\"\");\n        setLastName(\"\");\n        setEmail(\"\");\n        setRole(\"\");\n        setEditingUser(null);\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormLoading(true);\n        setError(\"\");\n        try {\n            const userData = {\n                firstName,\n                lastName,\n                email,\n                role\n            };\n            if (editingUser) {\n                await _lib_services_users_service__WEBPACK_IMPORTED_MODULE_11__.usersService.updateUser(editingUser.id, userData);\n                setSuccess(\"User updated successfully\");\n            } else {\n                await _lib_services_auth_service__WEBPACK_IMPORTED_MODULE_13__.authService.createUserByAdmin(userData);\n                setSuccess(\"User created successfully\");\n            }\n            await loadUsers();\n            setDialogOpen(false);\n            resetForm();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to save user\");\n        } finally{\n            setFormLoading(false);\n        }\n    };\n    const handleEdit = (user)=>{\n        setEditingUser(user);\n        setFirstName(user.firstName);\n        setLastName(user.lastName);\n        setEmail(user.email);\n        setRole(user.role);\n        setDialogOpen(true);\n    };\n    const handleDelete = async (user)=>{\n        if (confirm(\"Are you sure you want to delete \".concat(user.firstName, \" \").concat(user.lastName, \"?\"))) {\n            try {\n                await _lib_services_users_service__WEBPACK_IMPORTED_MODULE_11__.usersService.deleteUser(user.id);\n                setSuccess(\"User deleted successfully\");\n                await loadUsers();\n            } catch (error) {\n                var _error_response_data, _error_response;\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete user\");\n            }\n        }\n    };\n    const openCreateDialog = ()=>{\n        resetForm();\n        setDialogOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Users\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: dialogOpen,\n                        onOpenChange: setDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: openCreateDialog,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Add User\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                className: \"sm:max-w-[425px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                children: editingUser ? \"Edit User\" : \"Create New User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                children: editingUser ? \"Update user information.\" : \"Create a new user account.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4 py-4\",\n                                                children: [\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                                                        variant: \"destructive\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"firstName\",\n                                                                        children: \"First Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 187,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"firstName\",\n                                                                        value: firstName,\n                                                                        onChange: (e)=>setFirstName(e.target.value),\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"lastName\",\n                                                                        children: \"Last Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"lastName\",\n                                                                        value: lastName,\n                                                                        onChange: (e)=>setLastName(e.target.value),\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"email\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"email\",\n                                                                type: \"email\",\n                                                                value: email,\n                                                                onChange: (e)=>setEmail(e.target.value),\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"role\",\n                                                                children: \"Role\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                value: role,\n                                                                onValueChange: setRole,\n                                                                required: true,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                            placeholder: \"Select role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                        children: USER_ROLES.map((roleOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                value: roleOption.value,\n                                                                                children: roleOption.label\n                                                                            }, roleOption.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                                lineNumber: 223,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    disabled: formLoading,\n                                                    children: formLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            editingUser ? \"Updating...\" : \"Creating...\"\n                                                        ]\n                                                    }, void 0, true) : editingUser ? \"Update User\" : \"Create User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            placeholder: \"Search users...\",\n                            className: \"pl-8\",\n                            value: searchTerm,\n                            onChange: (e)=>setSearchTerm(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this),\n            error && !dialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-md border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                        children: \"Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                        children: \"Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                        children: \"Organization\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                        className: \"w-[50px]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                            children: filteredUsers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                    colSpan: 6,\n                                    className: \"text-center py-8 text-muted-foreground\",\n                                    children: \"No users found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 17\n                            }, this) : filteredUsers.map((user)=>{\n                                var _user_organization;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                            className: \"font-medium\",\n                                            children: [\n                                                user.firstName,\n                                                \" \",\n                                                user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                            children: user.role.replace(\"_\", \" \")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                            children: ((_user_organization = user.organization) === null || _user_organization === void 0 ? void 0 : _user_organization.organizationName) || \"No Organization\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                            children: user.emailVerified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"bg-green-50 text-green-700 border-green-200\",\n                                                children: \"Verified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 25\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"bg-yellow-50 text-yellow-700 border-yellow-200\",\n                                                children: \"Pending\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"h-8 w-8 p-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Open menu\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuLabel, {\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleEdit(user),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Edit user\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleDelete(user),\n                                                                className: \"text-red-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Delete user\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, user.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\users\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(UsersPage, \"6yji6QQXBNXC0pu9H+AJntIJVYM=\");\n_c = UsersPage;\nvar _c;\n$RefreshReg$(_c, \"UsersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/users/page.tsx\n"));

/***/ })

});