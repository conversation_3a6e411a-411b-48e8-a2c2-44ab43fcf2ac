import { Injectable, ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateFinancingAgentDto, UpdateFinancingAgentDto } from './dto';

@Injectable()
export class FinancingAgentsService {
  constructor(private prisma: PrismaService) {}

  async create(createFinancingAgentDto: CreateFinancingAgentDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can create financing agents');
    }

    // Check if agent with same name already exists
    const existingAgent = await this.prisma.financingAgent.findUnique({
      where: { agentName: createFinancingAgentDto.agentName },
    });

    if (existingAgent) {
      throw new ConflictException('Financing agent with this name already exists');
    }

    return this.prisma.financingAgent.create({
      data: createFinancingAgentDto,
    });
  }

  async findAll() {
    return this.prisma.financingAgent.findMany({
      where: { deleted: false },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: number) {
    const agent = await this.prisma.financingAgent.findUnique({
      where: { id, deleted: false },
    });

    if (!agent) {
      throw new NotFoundException('Financing agent not found');
    }

    return agent;
  }

  async update(id: number, updateFinancingAgentDto: UpdateFinancingAgentDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can update financing agents');
    }

    // Check if agent exists
    const existingAgent = await this.prisma.financingAgent.findUnique({
      where: { id, deleted: false },
    });

    if (!existingAgent) {
      throw new NotFoundException('Financing agent not found');
    }

    // Check if new name conflicts with existing agent (if name is being updated)
    if (updateFinancingAgentDto.agentName && updateFinancingAgentDto.agentName !== existingAgent.agentName) {
      const conflictingAgent = await this.prisma.financingAgent.findUnique({
        where: { agentName: updateFinancingAgentDto.agentName },
      });

      if (conflictingAgent) {
        throw new ConflictException('Financing agent with this name already exists');
      }
    }

    return this.prisma.financingAgent.update({
      where: { id },
      data: updateFinancingAgentDto,
    });
  }

  async remove(id: number, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can delete financing agents');
    }

    // Check if agent exists
    const existingAgent = await this.prisma.financingAgent.findUnique({
      where: { id, deleted: false },
    });

    if (!existingAgent) {
      throw new NotFoundException('Financing agent not found');
    }

    // Soft delete
    return this.prisma.financingAgent.update({
      where: { id },
      data: { deleted: true },
    });
  }
}
