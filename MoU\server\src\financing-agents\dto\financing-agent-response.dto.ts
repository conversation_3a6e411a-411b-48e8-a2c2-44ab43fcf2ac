import { ApiProperty } from '@nestjs/swagger';

export class FinancingAgentResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the financing agent',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Name of the financing agent',
    example: 'World Bank',
  })
  agentName: string;

  @ApiProperty({
    description: 'Description of the financing agent',
    example: 'International financial institution providing loans and grants',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Contact information for the financing agent',
    example: '<EMAIL>, +1-202-473-1000',
    required: false,
  })
  contactInfo?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
