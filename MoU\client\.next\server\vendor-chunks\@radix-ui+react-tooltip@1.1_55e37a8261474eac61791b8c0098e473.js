"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473";
exports.ids = ["vendor-chunks/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473/node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473/node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_c325a527ee623bb35f115eb421b60f39/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_003328fe74c3632a3c9c5ce511d98a5d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_25d1e76761af64c65b25ef237e549d8d/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@19.1.6_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._3fa130616be06a95782b33ef544dc151/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._4572a068bb2a4a1b47f2889d3c644b5b/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._2ac352e659c445f8ff2bfd8f4fda9236/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_e3803c74fa3732ab7d036a7d08888245/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.6_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_07f3fde21329b0c35912c2be5d860183/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_952a1481d55877aaf09731d2a2c146e5/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // packages/react/tooltip/src/Tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipProvider.useEffect\": ()=>{\n            const skipDelayTimer = skipDelayTimerRef.current;\n            return ({\n                \"TooltipProvider.useEffect\": ()=>window.clearTimeout(skipDelayTimer)\n            })[\"TooltipProvider.useEffect\"];\n        }\n    }[\"TooltipProvider.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayed,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                setIsOpenDelayed(false);\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout({\n                    \"TooltipProvider.useCallback\": ()=>setIsOpenDelayed(true)\n                }[\"TooltipProvider.useCallback\"], skipDelayDuration);\n            }\n        }[\"TooltipProvider.useCallback\"], [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": (inTransit)=>{\n                isPointerInTransitRef.current = inTransit;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen = false, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: {\n            \"Tooltip.useControllableState\": (open2)=>{\n                if (open2) {\n                    providerContext.onOpen();\n                    document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n                } else {\n                    providerContext.onClose();\n                }\n                onOpenChange?.(open2);\n            }\n        }[\"Tooltip.useControllableState\"]\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Tooltip.useMemo[stateAttribute]\": ()=>{\n            return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n        }\n    }[\"Tooltip.useMemo[stateAttribute]\"], [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            wasOpenDelayedRef.current = false;\n            setOpen(true);\n        }\n    }[\"Tooltip.useCallback[handleOpen]\"], [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleClose]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            setOpen(false);\n        }\n    }[\"Tooltip.useCallback[handleClose]\"], [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = window.setTimeout({\n                \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n                    wasOpenDelayedRef.current = true;\n                    setOpen(true);\n                    openTimerRef.current = 0;\n                }\n            }[\"Tooltip.useCallback[handleDelayedOpen]\"], delayDuration);\n        }\n    }[\"Tooltip.useCallback[handleDelayedOpen]\"], [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Tooltip.useEffect\": ()=>{\n            return ({\n                \"Tooltip.useEffect\": ()=>{\n                    if (openTimerRef.current) {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            })[\"Tooltip.useEffect\"];\n        }\n    }[\"Tooltip.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (providerContext.isOpenDelayed) handleDelayedOpen();\n                    else handleOpen();\n                }\n            }[\"Tooltip.useCallback\"], [\n                providerContext.isOpenDelayed,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (disableHoverableContent) {\n                        handleClose();\n                    } else {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            }[\"Tooltip.useCallback\"], [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipTrigger.useCallback[handlePointerUp]\": ()=>isPointerDownRef.current = false\n    }[\"TooltipTrigger.useCallback[handlePointerUp]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipTrigger.useEffect\": ()=>{\n            return ({\n                \"TooltipTrigger.useEffect\": ()=>document.removeEventListener(\"pointerup\", handlePointerUp)\n            })[\"TooltipTrigger.useEffect\"];\n        }\n    }[\"TooltipTrigger.useEffect\"], [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\": ()=>{\n            setPointerGraceArea(null);\n            onPointerInTransitChange(false);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleCreateGraceArea]\": (event, hoverTarget)=>{\n            const currentTarget = event.currentTarget;\n            const exitPoint = {\n                x: event.clientX,\n                y: event.clientY\n            };\n            const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n            const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n            const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n            const graceArea = getHull([\n                ...paddedExitPoints,\n                ...hoverTargetPoints\n            ]);\n            setPointerGraceArea(graceArea);\n            onPointerInTransitChange(true);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleCreateGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            return ({\n                \"TooltipContentHoverable.useEffect\": ()=>handleRemoveGraceArea()\n            })[\"TooltipContentHoverable.useEffect\"];\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (trigger && content) {\n                const handleTriggerLeave = {\n                    \"TooltipContentHoverable.useEffect.handleTriggerLeave\": (event)=>handleCreateGraceArea(event, content)\n                }[\"TooltipContentHoverable.useEffect.handleTriggerLeave\"];\n                const handleContentLeave = {\n                    \"TooltipContentHoverable.useEffect.handleContentLeave\": (event)=>handleCreateGraceArea(event, trigger)\n                }[\"TooltipContentHoverable.useEffect.handleContentLeave\"];\n                trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n                content.addEventListener(\"pointerleave\", handleContentLeave);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>{\n                        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                        content.removeEventListener(\"pointerleave\", handleContentLeave);\n                    }\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (pointerGraceArea) {\n                const handleTrackPointerGrace = {\n                    \"TooltipContentHoverable.useEffect.handleTrackPointerGrace\": (event)=>{\n                        const target = event.target;\n                        const pointerPosition = {\n                            x: event.clientX,\n                            y: event.clientY\n                        };\n                        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                        if (hasEnteredTarget) {\n                            handleRemoveGraceArea();\n                        } else if (isPointerOutsideGraceArea) {\n                            handleRemoveGraceArea();\n                            onClose();\n                        }\n                    }\n                }[\"TooltipContentHoverable.useEffect.handleTrackPointerGrace\"];\n                document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace)\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            document.addEventListener(TOOLTIP_OPEN, onClose);\n            return ({\n                \"TooltipContentImpl.useEffect\": ()=>document.removeEventListener(TOOLTIP_OPEN, onClose)\n            })[\"TooltipContentImpl.useEffect\"];\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            if (context.trigger) {\n                const handleScroll = {\n                    \"TooltipContentImpl.useEffect.handleScroll\": (event)=>{\n                        const target = event.target;\n                        if (target?.contains(context.trigger)) onClose();\n                    }\n                }[\"TooltipContentImpl.useEffect.handleScroll\"];\n                window.addEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n                return ({\n                    \"TooltipContentImpl.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                            capture: true\n                        })\n                })[\"TooltipContentImpl.useEffect\"];\n            }\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const xi = polygon[i].x;\n        const yi = polygon[i].y;\n        const xj = polygon[j].x;\n        const yj = polygon[j].y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473/node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ })

};
;