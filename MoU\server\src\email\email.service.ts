import { MailerService } from "@nestjs-modules/mailer";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly mailerService: MailerService,
    private configService: ConfigService
  ) { }

  private getBaseContext() {
    return {
      year: new Date().getFullYear(),
      logoUrl: this.configService.get('LOGO_URL') || 'https://www.moh.gov.rw/assets/logo.png',
      supportEmail: this.configService.get('SUPPORT_EMAIL') || '<EMAIL>',
      supportPhone: this.configService.get('SUPPORT_PHONE') || '+*********** 000',
      frontendUrl: this.configService.get('FRONTEND_URL') || 'http://localhost:3000',
      socialLinks: {
        twitter: this.configService.get('TWITTER_URL') || 'https://twitter.com/RwandaHealth',
        facebook: this.configService.get('FACEBOOK_URL') || 'https://facebook.com/RwandaHealth',
        linkedin: this.configService.get('LINKEDIN_URL') || 'https://linkedin.com/company/ministry-of-health-rwanda'
      },
      unsubscribeUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/email-preferences/unsubscribe`,
      preferencesUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/email-preferences`
    };
  }

  async sendWelcomeEmail(user: { email: string; firstName: string; lastName: string }) {
    try {
      const context = {
        ...this.getBaseContext(),
        firstName: user.firstName,
        lastName: user.lastName,
        name: `${user.firstName} ${user.lastName}`,
        dashboardUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/dashboard`
      };

      await this.mailerService.sendMail({
        to: user.email,
        subject: 'Welcome to MoU Management System - Ministry of Health',
        template: 'welcome',
        context
      });

      this.logger.log(`Welcome email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(`Failed to send welcome email to ${user.email}`, error.stack);
      throw error;
    }
  }

  async sendEmailVerificationEmail(user: { email: string; firstName: string; lastName: string; verificationToken: string; tempPassword?: string }) {
    try {
      const context = {
        ...this.getBaseContext(),
        firstName: user.firstName,
        lastName: user.lastName,
        name: `${user.firstName} ${user.lastName}`,
        verificationUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/auth/verify-email?token=${user.verificationToken}`,
        tempPassword: user.tempPassword
      };

      await this.mailerService.sendMail({
        to: user.email,
        subject: 'Verify Your Email Address - MoU Management System',
        template: 'email-verification',
        context
      });

      this.logger.log(`Email verification email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(`Failed to send email verification email to ${user.email}`, error.stack);
      throw error;
    }
  }



  async sendPasswordResetEmail(user: { email: string; firstName: string; lastName: string; resetToken: string }) {
    try {
      const context = {
        ...this.getBaseContext(),
        firstName: user.firstName,
        lastName: user.lastName,
        name: `${user.firstName} ${user.lastName}`,
        resetUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/auth/reset-password?token=${user.resetToken}`
      };

      await this.mailerService.sendMail({
        to: user.email,
        subject: 'Reset Your Password',
        template: 'reset-password',
        context
      });

      this.logger.log(`Password reset email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${user.email}`, error.stack);
      throw error;
    }
  }

  async sendInvitationEmail(invitation: {
    email: string;
    invitationToken: string;
    inviterName: string;
    companyName: string;
    companyDescription?: string;
    role: string
  }) {
    try {
      const context = {
        ...this.getBaseContext(),
        inviterName: invitation.inviterName,
        companyName: invitation.companyName,
        companyDescription: invitation.companyDescription || `Join ${invitation.companyName} on Supportive`,
        role: invitation.role,
        invitationUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/auth/accept-invitation?token=${invitation.invitationToken}`
      };

      await this.mailerService.sendMail({
        to: invitation.email,
        subject: `You've Been Invited to Join ${invitation.companyName} on Supportive`,
        template: 'invitation',
        context
      });

      this.logger.log(`Invitation email sent to ${invitation.email}`);
    } catch (error) {
      this.logger.error(`Failed to send invitation email to ${invitation.email}`, error.stack);
      throw error;
    }
  }

  async sendHostBookingNotification(booking: {
    hostEmail: string;
    hostName: string;
    propertyName: string;
    bookingId: string;
    checkIn: string;
    checkOut: string;
    guests: number;
    totalPayout: number;
    guestName: string;
    guestReviews?: string;
    reviewCount?: number;
    guestMessage?: string;
  }) {
    try {
      const context = {
        ...this.getBaseContext(),
        ...booking,
        approveUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/host/bookings/${booking.bookingId}/approve`,
        rejectUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/host/bookings/${booking.bookingId}/reject`,
        dashboardUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/host/dashboard`
      };

      await this.mailerService.sendMail({
        to: booking.hostEmail,
        subject: `New Booking Request: ${booking.propertyName}`,
        template: 'new-booking',
        context
      });

      this.logger.log(`Booking notification email sent to ${booking.hostEmail}`);
    } catch (error) {
      this.logger.error(`Failed to send booking notification email to ${booking.hostEmail}`, error.stack);
      throw error;
    }
  }

  async sendBookingApprovalNotification(booking: {
    guestEmail: string;
    guestName: string;
    propertyName: string;
    propertyAddress: string;
    propertyImageUrl?: string;
    bookingId: string;
    checkIn: string;
    checkOut: string;
    guests: number;
    totalAmount: number;
    hostName?: string;
    hostMessage?: string;
  }) {
    try {
      const context = {
        ...this.getBaseContext(),
        ...booking,
        bookingDetailsUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/trips/${booking.bookingId}`,
        tripsUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/trips`,
        cancellationPolicyUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/policies/cancellation`
      };

      await this.mailerService.sendMail({
        to: booking.guestEmail,
        subject: 'Booking Confirmed! Your Reservation Details',
        template: 'approve',
        context
      });

      this.logger.log(`Booking approval email sent to ${booking.guestEmail}`);
    } catch (error) {
      this.logger.error(`Failed to send booking approval email to ${booking.guestEmail}`, error.stack);
      throw error;
    }
  }

  async sendBookingRejectionNotification(booking: {
    guestEmail: string;
    guestName: string;
    propertyName: string;
    bookingId: string;
    checkIn: string;
    checkOut: string;
    guests: number;
    hostMessage?: string;
  }) {
    try {
      const context = {
        ...this.getBaseContext(),
        ...booking,
        searchUrl: `${this.configService.get('FRONTEND_URL') || 'http://localhost:3000'}/search?checkin=${booking.checkIn}&checkout=${booking.checkOut}&guests=${booking.guests}`
      };

      await this.mailerService.sendMail({
        to: booking.guestEmail,
        subject: 'Update on Your Booking Request',
        template: 'reject',
        context
      });

      this.logger.log(`Booking rejection email sent to ${booking.guestEmail}`);
    } catch (error) {
      this.logger.error(`Failed to send booking rejection email to ${booking.guestEmail}`, error.stack);
      throw error;
    }
  }
}