"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, ArrowLeft, ArrowRight, Check } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { masterDataService } from "@/lib/services/master-data.service"

interface RegistrationData {
  // Step 1: Account Information
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  
  // Step 2: Organization Information
  organizationName: string
  organizationRegistrationNumber: string
  organizationPhoneNumber: string
  organizationEmail: string
  organizationWebsite: string
  homeCountryRepresentative: string
  rwandaRepresentative: string
  organizationRgbNumber: string
  organizationTypeId: number
  
  // Step 3: Address Information
  addresses: Array<{
    addressType: "HEADQUARTERS" | "RWANDA"
    country: string
    province?: string
    district?: string
    sector?: string
    cell?: string
    village?: string
    street: string
    avenue?: string
    poBox: string
    postalCode?: string
  }>
}

interface MultiStepRegistrationProps {
  onSuccess: () => void
  onCancel: () => void
}

export function MultiStepRegistration({ onSuccess, onCancel }: MultiStepRegistrationProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [organizationTypes, setOrganizationTypes] = useState<any[]>([])
  const { register } = useAuth()

  const [formData, setFormData] = useState<RegistrationData>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    organizationName: "",
    organizationRegistrationNumber: "",
    organizationPhoneNumber: "",
    organizationEmail: "",
    organizationWebsite: "",
    homeCountryRepresentative: "",
    rwandaRepresentative: "",
    organizationRgbNumber: "",
    organizationTypeId: 0,
    addresses: [
      {
        addressType: "HEADQUARTERS",
        country: "",
        street: "",
        poBox: "",
      }
    ]
  })

  useEffect(() => {
    const loadOrganizationTypes = async () => {
      try {
        const types = await masterDataService.getOrganizationTypes()
        setOrganizationTypes(types)
      } catch (error) {
        console.error("Failed to load organization types:", error)
      }
    }
    loadOrganizationTypes()
  }, [])

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const updateAddress = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      addresses: prev.addresses.map((addr, i) => 
        i === index ? { ...addr, [field]: value } : addr
      )
    }))
  }

  const addAddress = () => {
    if (formData.addresses.length < 2) {
      setFormData(prev => ({
        ...prev,
        addresses: [...prev.addresses, {
          addressType: "RWANDA",
          country: "Rwanda",
          street: "",
          poBox: "",
        }]
      }))
    }
  }

  const removeAddress = (index: number) => {
    if (formData.addresses.length > 1) {
      setFormData(prev => ({
        ...prev,
        addresses: prev.addresses.filter((_, i) => i !== index)
      }))
    }
  }

  const validateStep = (step: number): boolean => {
    setError("")
    
    switch (step) {
      case 1:
        if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
          setError("Please fill in all required fields")
          return false
        }
        if (formData.password !== formData.confirmPassword) {
          setError("Passwords do not match")
          return false
        }
        if (formData.password.length < 8) {
          setError("Password must be at least 8 characters long")
          return false
        }
        break
      
      case 2:
        if (!formData.organizationName || !formData.organizationRegistrationNumber || 
            !formData.organizationPhoneNumber || !formData.organizationEmail ||
            !formData.homeCountryRepresentative || !formData.rwandaRepresentative ||
            !formData.organizationRgbNumber || !formData.organizationTypeId) {
          setError("Please fill in all required organization fields")
          return false
        }
        break
      
      case 3:
        if (formData.addresses.length === 0) {
          setError("At least one address is required")
          return false
        }
        for (const addr of formData.addresses) {
          if (!addr.country || !addr.street || !addr.poBox) {
            setError("Please fill in all required address fields")
            return false
          }
        }
        break
    }
    
    return true
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1)
  }

  const handleSubmit = async () => {
    if (!validateStep(3)) return

    setLoading(true)
    try {
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        organization: {
          organizationName: formData.organizationName,
          organizationRegistrationNumber: formData.organizationRegistrationNumber,
          organizationPhoneNumber: formData.organizationPhoneNumber,
          organizationEmail: formData.organizationEmail,
          organizationWebsite: formData.organizationWebsite || undefined,
          homeCountryRepresentative: formData.homeCountryRepresentative,
          rwandaRepresentative: formData.rwandaRepresentative,
          organizationRgbNumber: formData.organizationRgbNumber,
          organizationTypeId: formData.organizationTypeId,
          addresses: formData.addresses
        }
      }

      await register(registrationData)
      onSuccess()
    } catch (error: any) {
      setError(error.response?.data?.message || "Registration failed. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3].map((step) => (
        <React.Fragment key={step}>
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step <= currentStep ? 'bg-cyan-600 text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            {step < currentStep ? <Check className="w-4 h-4" /> : step}
          </div>
          {step < 3 && (
            <div className={`w-16 h-1 ${
              step < currentStep ? 'bg-cyan-600' : 'bg-gray-200'
            }`} />
          )}
        </React.Fragment>
      ))}
    </div>
  )

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">Partner Registration</CardTitle>
        <CardDescription className="text-center">
          Register your organization as a partner with the Ministry of Health
        </CardDescription>
        {renderStepIndicator()}
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Step 1: Account Information */}
        {currentStep === 1 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-4">Account Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => updateFormData("firstName", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => updateFormData("lastName", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => updateFormData("email", e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password *</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => updateFormData("password", e.target.value)}
                required
              />
              <p className="text-sm text-gray-600">
                Password must be at least 8 characters with uppercase, lowercase, and number/special character
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password *</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                required
              />
            </div>
          </div>
        )}

        {/* Step 2: Organization Information */}
        {currentStep === 2 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-4">Organization Information</h3>
            <div className="space-y-2">
              <Label htmlFor="organizationName">Organization Name *</Label>
              <Input
                id="organizationName"
                value={formData.organizationName}
                onChange={(e) => updateFormData("organizationName", e.target.value)}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="organizationRegistrationNumber">Registration Number *</Label>
                <Input
                  id="organizationRegistrationNumber"
                  value={formData.organizationRegistrationNumber}
                  onChange={(e) => updateFormData("organizationRegistrationNumber", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="organizationRgbNumber">RGB Number *</Label>
                <Input
                  id="organizationRgbNumber"
                  value={formData.organizationRgbNumber}
                  onChange={(e) => updateFormData("organizationRgbNumber", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="organizationPhoneNumber">Phone Number *</Label>
                <Input
                  id="organizationPhoneNumber"
                  value={formData.organizationPhoneNumber}
                  onChange={(e) => updateFormData("organizationPhoneNumber", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="organizationEmail">Organization Email *</Label>
                <Input
                  id="organizationEmail"
                  type="email"
                  value={formData.organizationEmail}
                  onChange={(e) => updateFormData("organizationEmail", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="organizationWebsite">Website (Optional)</Label>
              <Input
                id="organizationWebsite"
                value={formData.organizationWebsite}
                onChange={(e) => updateFormData("organizationWebsite", e.target.value)}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="homeCountryRepresentative">Home Country Representative *</Label>
                <Input
                  id="homeCountryRepresentative"
                  value={formData.homeCountryRepresentative}
                  onChange={(e) => updateFormData("homeCountryRepresentative", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rwandaRepresentative">Rwanda Representative *</Label>
                <Input
                  id="rwandaRepresentative"
                  value={formData.rwandaRepresentative}
                  onChange={(e) => updateFormData("rwandaRepresentative", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="organizationTypeId">Organization Type *</Label>
              <Select
                value={formData.organizationTypeId.toString()}
                onValueChange={(value) => updateFormData("organizationTypeId", parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select organization type" />
                </SelectTrigger>
                <SelectContent>
                  {organizationTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id.toString()}>
                      {type.typeName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Step 3: Address Information */}
        {currentStep === 3 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-4">Address Information</h3>
            <p className="text-sm text-gray-600 mb-4">
              Provide at least one address. You can add both headquarters and Rwanda addresses.
            </p>

            {formData.addresses.map((address, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">
                    {address.addressType === "HEADQUARTERS" ? "Headquarters Address" : "Rwanda Address"}
                  </h4>
                  {formData.addresses.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeAddress(index)}
                    >
                      Remove
                    </Button>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Address Type *</Label>
                  <Select
                    value={address.addressType}
                    onValueChange={(value) => updateAddress(index, "addressType", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HEADQUARTERS">Headquarters</SelectItem>
                      <SelectItem value="RWANDA">Rwanda</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Country *</Label>
                    <Input
                      value={address.country}
                      onChange={(e) => updateAddress(index, "country", e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Province/State</Label>
                    <Input
                      value={address.province || ""}
                      onChange={(e) => updateAddress(index, "province", e.target.value)}
                    />
                  </div>
                </div>

                {address.addressType === "RWANDA" && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>District</Label>
                        <Input
                          value={address.district || ""}
                          onChange={(e) => updateAddress(index, "district", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Sector</Label>
                        <Input
                          value={address.sector || ""}
                          onChange={(e) => updateAddress(index, "sector", e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Cell</Label>
                        <Input
                          value={address.cell || ""}
                          onChange={(e) => updateAddress(index, "cell", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Village</Label>
                        <Input
                          value={address.village || ""}
                          onChange={(e) => updateAddress(index, "village", e.target.value)}
                        />
                      </div>
                    </div>
                  </>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Street *</Label>
                    <Input
                      value={address.street}
                      onChange={(e) => updateAddress(index, "street", e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Avenue</Label>
                    <Input
                      value={address.avenue || ""}
                      onChange={(e) => updateAddress(index, "avenue", e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>P.O. Box *</Label>
                    <Input
                      value={address.poBox}
                      onChange={(e) => updateAddress(index, "poBox", e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Postal Code</Label>
                    <Input
                      value={address.postalCode || ""}
                      onChange={(e) => updateAddress(index, "postalCode", e.target.value)}
                    />
                  </div>
                </div>
              </div>
            ))}

            {formData.addresses.length < 2 && (
              <Button
                type="button"
                variant="outline"
                onClick={addAddress}
                className="w-full"
              >
                Add {formData.addresses.length === 0 ? "Address" :
                     formData.addresses[0].addressType === "HEADQUARTERS" ? "Rwanda Address" : "Headquarters Address"}
              </Button>
            )}
          </div>
        )}
        
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? onCancel : handlePrevious}
            disabled={loading}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {currentStep === 1 ? "Cancel" : "Previous"}
          </Button>

          {currentStep < 3 ? (
            <Button type="button" onClick={handleNext} disabled={loading}>
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button type="button" onClick={handleSubmit} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Registering...
                </>
              ) : (
                "Complete Registration"
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
