{"c": ["app/layout", "app/dashboard/users/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/dashboard/users/page.tsx", "(app-pages-browser)/./components/ui/badge.tsx", "(app-pages-browser)/./lib/services/organization.service.ts", "(app-pages-browser)/./lib/services/users.service.ts", "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CLENOVO%5C%5CDesktop%5C%5CCODE%5C%5CWORK%5C%5CMoH%5C%5CMoU%5C%5Cclient%5C%5Capp%5C%5Cdashboard%5C%5Cusers%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}