"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancingSchemesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FinancingSchemesService = class FinancingSchemesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createFinancingSchemeDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can create financing schemes');
        }
        const existingScheme = await this.prisma.financingScheme.findUnique({
            where: { schemeName: createFinancingSchemeDto.schemeName },
        });
        if (existingScheme) {
            throw new common_1.ConflictException('Financing scheme with this name already exists');
        }
        return this.prisma.financingScheme.create({
            data: createFinancingSchemeDto,
        });
    }
    async findAll() {
        return this.prisma.financingScheme.findMany({
            where: { deleted: false },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOne(id) {
        const scheme = await this.prisma.financingScheme.findUnique({
            where: { id, deleted: false },
        });
        if (!scheme) {
            throw new common_1.NotFoundException('Financing scheme not found');
        }
        return scheme;
    }
    async update(id, updateFinancingSchemeDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can update financing schemes');
        }
        const existingScheme = await this.prisma.financingScheme.findUnique({
            where: { id, deleted: false },
        });
        if (!existingScheme) {
            throw new common_1.NotFoundException('Financing scheme not found');
        }
        if (updateFinancingSchemeDto.schemeName && updateFinancingSchemeDto.schemeName !== existingScheme.schemeName) {
            const conflictingScheme = await this.prisma.financingScheme.findUnique({
                where: { schemeName: updateFinancingSchemeDto.schemeName },
            });
            if (conflictingScheme) {
                throw new common_1.ConflictException('Financing scheme with this name already exists');
            }
        }
        return this.prisma.financingScheme.update({
            where: { id },
            data: updateFinancingSchemeDto,
        });
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can delete financing schemes');
        }
        const existingScheme = await this.prisma.financingScheme.findUnique({
            where: { id, deleted: false },
        });
        if (!existingScheme) {
            throw new common_1.NotFoundException('Financing scheme not found');
        }
        return this.prisma.financingScheme.update({
            where: { id },
            data: { deleted: true },
        });
    }
};
exports.FinancingSchemesService = FinancingSchemesService;
exports.FinancingSchemesService = FinancingSchemesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FinancingSchemesService);
//# sourceMappingURL=financing-schemes.service.js.map