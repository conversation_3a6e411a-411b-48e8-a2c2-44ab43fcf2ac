"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCareProvidersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let HealthCareProvidersService = class HealthCareProvidersService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createHealthCareProviderDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can create health care providers');
        }
        const existingProvider = await this.prisma.healthCareProvider.findUnique({
            where: { providerName: createHealthCareProviderDto.providerName },
        });
        if (existingProvider) {
            throw new common_1.ConflictException('Health care provider with this name already exists');
        }
        return this.prisma.healthCareProvider.create({
            data: createHealthCareProviderDto,
        });
    }
    async findAll() {
        return this.prisma.healthCareProvider.findMany({
            where: { deleted: false },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOne(id) {
        const provider = await this.prisma.healthCareProvider.findUnique({
            where: { id, deleted: false },
        });
        if (!provider) {
            throw new common_1.NotFoundException('Health care provider not found');
        }
        return provider;
    }
    async update(id, updateHealthCareProviderDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can update health care providers');
        }
        const existingProvider = await this.prisma.healthCareProvider.findUnique({
            where: { id, deleted: false },
        });
        if (!existingProvider) {
            throw new common_1.NotFoundException('Health care provider not found');
        }
        if (updateHealthCareProviderDto.providerName && updateHealthCareProviderDto.providerName !== existingProvider.providerName) {
            const conflictingProvider = await this.prisma.healthCareProvider.findUnique({
                where: { providerName: updateHealthCareProviderDto.providerName },
            });
            if (conflictingProvider) {
                throw new common_1.ConflictException('Health care provider with this name already exists');
            }
        }
        return this.prisma.healthCareProvider.update({
            where: { id },
            data: updateHealthCareProviderDto,
        });
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can delete health care providers');
        }
        const existingProvider = await this.prisma.healthCareProvider.findUnique({
            where: { id, deleted: false },
        });
        if (!existingProvider) {
            throw new common_1.NotFoundException('Health care provider not found');
        }
        return this.prisma.healthCareProvider.update({
            where: { id },
            data: { deleted: true },
        });
    }
};
exports.HealthCareProvidersService = HealthCareProvidersService;
exports.HealthCareProvidersService = HealthCareProvidersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], HealthCareProvidersService);
//# sourceMappingURL=health-care-providers.service.js.map