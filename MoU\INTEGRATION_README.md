# MoU Management System - Frontend-Backend Integration

## Overview

This document outlines the complete integration between the frontend (Next.js) and backend (NestJS) for the MoU Management System. The integration includes multi-step partner registration, admin dashboard functionality, and comprehensive API integration.

## Key Features Implemented

### 1. Multi-Step Partner Registration Flow

**Location**: `/client/app/signup/page.tsx` and `/client/components/multi-step-registration.tsx`

**Flow**:
1. **Step 1**: Account Information (name, email, password)
2. **Step 2**: Organization Information (details, type, representatives)
3. **Step 3**: Address Information (headquarters and/or Rwanda addresses)
4. **Step 4**: Email verification flow

**API Integration**: Uses `/auth/register` endpoint with complete organization data structure.

### 2. Enhanced Authentication System

**Location**: `/client/contexts/auth-context.tsx` and `/client/lib/services/auth.service.ts`

**Features**:
- JWT token management with refresh token support
- Automatic token refresh on 401 errors
- Proper error handling and user feedback
- Integration with backend `/auth/login`, `/auth/refresh-token`, `/auth/me` endpoints

### 3. Admin Dashboard Integration

**Location**: `/client/app/dashboard/admin/page.tsx`

**Features**:
- Centralized admin interface for all master data entities
- Tabbed interface for different entity types
- Real-time data loading from backend APIs
- Role-based access control (ADMIN only)

### 4. User Management System

**Location**: `/client/app/dashboard/users/page.tsx`

**Features**:
- Complete CRUD operations for users
- Integration with `/users` and `/auth/create-user` endpoints
- Role-based user creation and management
- Organization assignment for non-admin users

### 5. Master Data Management

**Entities Integrated**:
- Budget Types (`/budget-types`)
- Funding Sources (`/funding-sources`)
- Funding Units (`/funding-units`)
- Organization Types (`/organization-types`)

**Location**: `/client/lib/services/master-data.service.ts`

## API Configuration

### Environment Variables

**Frontend** (`.env.local`):
```
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
```

**Backend** (`.env`):
```
PORT=8080
DATABASE_URL="postgresql://postgres:123456@localhost:5432/mou?schema=public"
JWT_SECRET=secret
BACKEND_URL=http://localhost:8080
FE_URL=http://localhost:3000
ADMIN_FIRST_NAME=Valentin
ADMIN_LAST_NAME=Dushime
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Test@1234
```

### API Base Configuration

The frontend is configured to connect to the backend at `http://localhost:8080/api/v1` with automatic token management and refresh capabilities.

## Service Layer Architecture

### 1. Authentication Service
- Handles login, registration, token refresh
- Manages user session state
- Provides admin user creation functionality

### 2. Users Service
- CRUD operations for user management
- Integration with backend user endpoints
- Type-safe interfaces for user data

### 3. Master Data Service
- Unified service for all master data entities
- Consistent CRUD operations across all entity types
- Proper error handling and type safety

### 4. Organization Service
- Organization management functionality
- Address handling for multi-location organizations
- Integration with organization endpoints

## UI Components

### 1. Multi-Step Registration Component
- Reusable component for complex registration flows
- Form validation and error handling
- Progress indicator and navigation

### 2. Data Table Component
- Reusable table component with search and actions
- Consistent UI across all admin interfaces
- Built-in edit and delete functionality

### 3. Enhanced Sidebar Navigation
- Role-based menu items
- Admin-specific navigation options
- Consistent branding with MoH colors

## Error Handling

### Frontend Error Handling
- Comprehensive error messages from API responses
- User-friendly error displays
- Automatic token refresh on authentication errors

### Loading States
- Loading indicators for all async operations
- Skeleton loading for better UX
- Proper loading state management

## Security Features

### 1. Role-Based Access Control
- Admin-only pages and functionality
- Protected routes with authentication checks
- Proper authorization for sensitive operations

### 2. Token Management
- Secure token storage in localStorage
- Automatic token refresh
- Proper cleanup on logout

### 3. Input Validation
- Frontend form validation
- Backend DTO validation integration
- Consistent error messaging

## Branding and Design

### Ministry of Health Branding
- Cyan (#00BCD4) primary color scheme
- Roboto font family
- Professional government styling
- @moh.gov.rw email domain integration

## Testing and Development

### Running the Application

1. **Backend**:
   ```bash
   cd MoU/server
   npm install
   npm run start:dev
   ```

2. **Frontend**:
   ```bash
   cd MoU/client
   npm install
   npm run dev
   ```

### Default Admin Account
- Email: `<EMAIL>`
- Password: `Test@1234`

## Future Enhancements

### Recommended Next Steps
1. Add comprehensive unit and integration tests
2. Implement email verification UI flow
3. Add password reset functionality
4. Enhance error logging and monitoring
5. Add data export/import capabilities
6. Implement audit logging for admin actions

## API Endpoints Summary

### Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - Partner registration
- `POST /auth/create-user` - Admin user creation
- `POST /auth/refresh-token` - Token refresh
- `GET /auth/me` - Current user info

### User Management
- `GET /users` - List users
- `POST /users` - Create user
- `PATCH /users/:id` - Update user
- `DELETE /users/:id` - Delete user

### Master Data
- Budget Types: `/budget-types`
- Funding Sources: `/funding-sources`
- Funding Units: `/funding-units`
- Organization Types: `/organization-types`

All master data endpoints support full CRUD operations with proper authentication and authorization.
