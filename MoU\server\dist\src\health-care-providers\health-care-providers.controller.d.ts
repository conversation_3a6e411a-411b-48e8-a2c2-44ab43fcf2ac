import { HealthCareProvidersService } from './health-care-providers.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto } from './dto';
export declare class HealthCareProvidersController {
    private readonly healthCareProvidersService;
    constructor(healthCareProvidersService: HealthCareProvidersService);
    create(createHealthCareProviderDto: CreateHealthCareProviderDto, req: any): Promise<any>;
    findAll(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateHealthCareProviderDto: UpdateHealthCareProviderDto, req: any): Promise<any>;
    remove(id: number, req: any): Promise<any>;
}
