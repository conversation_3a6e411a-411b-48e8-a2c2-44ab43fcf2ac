"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainInterventionsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let DomainInterventionsService = class DomainInterventionsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createDomainInterventionDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can create domain interventions');
        }
        if (createDomainInterventionDto.parentId) {
            const parentDomain = await this.prisma.domainIntervention.findUnique({
                where: { id: createDomainInterventionDto.parentId, deleted: false },
            });
            if (!parentDomain) {
                throw new common_1.BadRequestException('Parent domain not found');
            }
        }
        return this.prisma.domainIntervention.create({
            data: createDomainInterventionDto,
            include: {
                parent: true,
                children: true,
            },
        });
    }
    async findAll() {
        return this.prisma.domainIntervention.findMany({
            where: { deleted: false },
            include: {
                parent: true,
                children: {
                    where: { deleted: false },
                    include: {
                        children: {
                            where: { deleted: false },
                            include: {
                                children: true,
                            },
                        },
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findTree() {
        return this.prisma.domainIntervention.findMany({
            where: {
                deleted: false,
                parentId: null,
            },
            include: {
                children: {
                    where: { deleted: false },
                    include: {
                        children: {
                            where: { deleted: false },
                            include: {
                                children: {
                                    where: { deleted: false },
                                    include: {
                                        children: {
                                            where: { deleted: false },
                                            include: {
                                                children: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOne(id) {
        const domain = await this.prisma.domainIntervention.findUnique({
            where: { id, deleted: false },
            include: {
                parent: true,
                children: {
                    where: { deleted: false },
                    include: {
                        children: {
                            where: { deleted: false },
                            include: {
                                children: true,
                            },
                        },
                    },
                },
            },
        });
        if (!domain) {
            throw new common_1.NotFoundException('Domain intervention not found');
        }
        return domain;
    }
    async update(id, updateDomainInterventionDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can update domain interventions');
        }
        const existingDomain = await this.prisma.domainIntervention.findUnique({
            where: { id, deleted: false },
        });
        if (!existingDomain) {
            throw new common_1.NotFoundException('Domain intervention not found');
        }
        if (updateDomainInterventionDto.parentId !== undefined) {
            if (updateDomainInterventionDto.parentId === id) {
                throw new common_1.BadRequestException('Domain cannot be its own parent');
            }
            if (updateDomainInterventionDto.parentId) {
                const parentDomain = await this.prisma.domainIntervention.findUnique({
                    where: { id: updateDomainInterventionDto.parentId, deleted: false },
                });
                if (!parentDomain) {
                    throw new common_1.BadRequestException('Parent domain not found');
                }
                const isCircular = await this.checkCircularReference(id, updateDomainInterventionDto.parentId);
                if (isCircular) {
                    throw new common_1.BadRequestException('Circular reference detected');
                }
            }
        }
        return this.prisma.domainIntervention.update({
            where: { id },
            data: updateDomainInterventionDto,
            include: {
                parent: true,
                children: true,
            },
        });
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can delete domain interventions');
        }
        const existingDomain = await this.prisma.domainIntervention.findUnique({
            where: { id, deleted: false },
            include: {
                children: {
                    where: { deleted: false },
                },
            },
        });
        if (!existingDomain) {
            throw new common_1.NotFoundException('Domain intervention not found');
        }
        if (existingDomain.children.length > 0) {
            throw new common_1.BadRequestException('Cannot delete domain with sub-domains. Delete sub-domains first.');
        }
        return this.prisma.domainIntervention.update({
            where: { id },
            data: { deleted: true },
        });
    }
    async checkCircularReference(domainId, parentId) {
        if (domainId === parentId) {
            return true;
        }
        const parent = await this.prisma.domainIntervention.findUnique({
            where: { id: parentId },
        });
        if (!parent || !parent.parentId) {
            return false;
        }
        return this.checkCircularReference(domainId, parent.parentId);
    }
};
exports.DomainInterventionsService = DomainInterventionsService;
exports.DomainInterventionsService = DomainInterventionsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DomainInterventionsService);
//# sourceMappingURL=domain-interventions.service.js.map