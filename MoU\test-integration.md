# MoU Management System - Integration Testing Guide

## Prerequisites

1. **Backend Setup**:
   ```bash
   cd MoU/server
   npm install
   npm run start:dev
   ```
   Backend should be running on `http://localhost:8080`

2. **Frontend Setup**:
   ```bash
   cd MoU/client
   npm install
   npm run dev
   ```
   Frontend should be running on `http://localhost:3000`

3. **Database**: Ensure PostgreSQL is running with the database configured in `.env`

## Test Scenarios

### 1. Admin Authentication Test

**Objective**: Verify admin login and access to admin features

**Steps**:
1. Navigate to `http://localhost:3000/login`
2. Login with admin credentials:
   - Email: `<EMAIL>`
   - Password: `Test@1234`
3. Verify successful login and redirect to dashboard
4. Check that admin-specific menu items are visible (Admin Dashboard)

**Expected Results**:
- ✅ Successful login
- ✅ JWT tokens stored in localStorage
- ✅ Admin Dashboard menu item visible
- ✅ User role displayed correctly

### 2. Multi-Step Partner Registration Test

**Objective**: Test the complete partner registration flow

**Steps**:
1. Navigate to `http://localhost:3000/signup`
2. Click "Start Registration Process"
3. **Step 1 - Account Information**:
   - First Name: `John`
   - Last Name: `Doe`
   - Email: `<EMAIL>`
   - Password: `Test@1234`
   - Confirm Password: `Test@1234`
   - Click "Next"

4. **Step 2 - Organization Information**:
   - Organization Name: `Test Organization`
   - Registration Number: `REG123456`
   - RGB Number: `RGB789`
   - Phone: `+************`
   - Email: `<EMAIL>`
   - Home Country Rep: `Jane Smith`
   - Rwanda Rep: `Paul Kagame`
   - Organization Type: Select from dropdown
   - Click "Next"

5. **Step 3 - Address Information**:
   - Add headquarters address
   - Add Rwanda address (optional)
   - Fill required fields (Country, Street, P.O. Box)
   - Click "Complete Registration"

**Expected Results**:
- ✅ Form validation works at each step
- ✅ Organization types loaded from API
- ✅ Registration submitted successfully
- ✅ Success message displayed
- ✅ Redirect to login with verification message

### 3. Admin Dashboard Test

**Objective**: Verify admin dashboard functionality

**Steps**:
1. Login as admin
2. Navigate to "Admin Dashboard"
3. Test each tab:
   - Budget Types
   - Funding Sources
   - Funding Units
   - Organization Types
4. Verify data loading from API
5. Test search functionality
6. Test navigation to individual entity pages

**Expected Results**:
- ✅ All tabs load data from backend
- ✅ Search functionality works
- ✅ Data displays correctly
- ✅ Action buttons work

### 4. User Management Test

**Objective**: Test user CRUD operations

**Steps**:
1. Login as admin
2. Navigate to "Users" page
3. **Create User**:
   - Click "Add User"
   - Fill form with valid data
   - Select role and organization
   - Submit form
4. **Edit User**:
   - Click edit on a user
   - Modify information
   - Save changes
5. **Delete User**:
   - Click delete on a user
   - Confirm deletion

**Expected Results**:
- ✅ User list loads from API
- ✅ Create user works with temp password generation
- ✅ Edit user updates successfully
- ✅ Delete user removes from list
- ✅ Organization dropdown populated

### 5. Budget Types Management Test

**Objective**: Test master data CRUD operations

**Steps**:
1. Login as admin
2. Navigate to "Budget Types"
3. **Create Budget Type**:
   - Click "Add Budget Type"
   - Enter type name
   - Submit
4. **Edit Budget Type**:
   - Click edit on existing type
   - Modify name
   - Save
5. **Delete Budget Type**:
   - Click delete
   - Confirm deletion

**Expected Results**:
- ✅ Budget types list loads
- ✅ Create operation works
- ✅ Edit operation updates data
- ✅ Delete operation removes item
- ✅ Search functionality works

### 6. API Integration Test

**Objective**: Verify all API endpoints are working

**API Endpoints to Test**:

**Authentication**:
- `POST /api/v1/auth/login` ✅
- `POST /api/v1/auth/register` ✅
- `POST /api/v1/auth/create-user` ✅
- `POST /api/v1/auth/refresh-token` ✅
- `GET /api/v1/auth/me` ✅

**Users**:
- `GET /api/v1/users` ✅
- `POST /api/v1/users` ✅
- `PATCH /api/v1/users/:id` ✅
- `DELETE /api/v1/users/:id` ✅

**Master Data**:
- `GET /api/v1/budget-types` ✅
- `POST /api/v1/budget-types` ✅
- `PATCH /api/v1/budget-types/:id` ✅
- `DELETE /api/v1/budget-types/:id` ✅

**Organizations**:
- `GET /api/v1/organizations` ✅
- `GET /api/v1/organization-types` ✅

### 7. Error Handling Test

**Objective**: Verify proper error handling

**Test Cases**:
1. **Invalid Login**:
   - Try login with wrong credentials
   - Verify error message displayed

2. **Network Errors**:
   - Stop backend server
   - Try to perform operations
   - Verify error handling

3. **Validation Errors**:
   - Submit forms with invalid data
   - Verify validation messages

4. **Token Expiry**:
   - Manually expire token
   - Verify automatic refresh or redirect

**Expected Results**:
- ✅ Proper error messages displayed
- ✅ Network errors handled gracefully
- ✅ Form validation works
- ✅ Token refresh works automatically

### 8. UI/UX Test

**Objective**: Verify user interface and experience

**Test Areas**:
1. **Responsive Design**:
   - Test on different screen sizes
   - Verify mobile compatibility

2. **Loading States**:
   - Verify loading indicators
   - Check for proper feedback

3. **Navigation**:
   - Test sidebar navigation
   - Verify breadcrumbs and routing

4. **Branding**:
   - Check MoH color scheme (cyan #00BCD4)
   - Verify Roboto font usage
   - Check professional styling

**Expected Results**:
- ✅ Responsive design works
- ✅ Loading states visible
- ✅ Navigation intuitive
- ✅ Branding consistent

## Common Issues and Solutions

### Issue 1: CORS Errors
**Solution**: Ensure backend CORS is configured for frontend URL

### Issue 2: API Connection Failed
**Solution**: 
- Check backend is running on port 8080
- Verify `.env.local` has correct API URL
- Check network connectivity

### Issue 3: Authentication Errors
**Solution**:
- Clear localStorage
- Check JWT secret configuration
- Verify admin user exists in database

### Issue 4: Database Connection
**Solution**:
- Check PostgreSQL is running
- Verify database URL in backend `.env`
- Run database migrations

## Performance Checklist

- ✅ API responses under 2 seconds
- ✅ Page load times acceptable
- ✅ No memory leaks in browser
- ✅ Efficient data loading
- ✅ Proper caching where appropriate

## Security Checklist

- ✅ JWT tokens properly managed
- ✅ Sensitive data not exposed
- ✅ Role-based access working
- ✅ Input validation on frontend and backend
- ✅ HTTPS ready (for production)

## Completion Criteria

All test scenarios should pass with ✅ status. Any failing tests should be documented with specific error details and steps to reproduce.
