import { PrismaService } from '../prisma/prisma.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto } from './dto';
export declare class HealthCareProvidersService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createHealthCareProviderDto: CreateHealthCareProviderDto, userId: string): Promise<any>;
    findAll(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateHealthCareProviderDto: UpdateHealthCareProviderDto, userId: string): Promise<any>;
    remove(id: number, userId: string): Promise<any>;
}
