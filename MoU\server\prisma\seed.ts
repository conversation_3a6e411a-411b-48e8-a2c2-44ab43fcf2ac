import { PrismaClient, UserRole } from "@prisma/client";
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  const organizationTypes = [
    { typeName: 'Non-Governmental Organization' },
    { typeName: 'Private Company' },
    { typeName: 'Government Agency' },
    { typeName: 'International Organization' },
    { typeName: 'Religious Institution' },
  ];

  for (const type of organizationTypes) {
    await prisma.organizationType.upsert({
      where: { typeName: type.typeName },
      update: {},
      create: {
        typeName: type.typeName,
      },
    });
  }

  console.log('✅ Seeded OrganizationType data.');

  const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD, 10);

  //send admin user from env
  const adminUser = {
    firstName: process.env.ADMIN_FIRST_NAME || 'Admin',
    lastName: process.env.ADMIN_LAST_NAME || 'User',
    email: process.env.ADMIN_EMAIL,
    password: hashedPassword,
  };

  await prisma.user.upsert({
    where: { email: adminUser.email },
    update: {},
    create: {
      firstName: adminUser.firstName,
      lastName: adminUser.lastName,
      email: adminUser.email,
      password: adminUser.password,
      role: UserRole.ADMIN,
      emailVerified: true,
    },
  });

  console.log('✅ Seeded Admin user data.');

}

main()
  .catch((e) => {
    console.error('❌ Error seeding data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
