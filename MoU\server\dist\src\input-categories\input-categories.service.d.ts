import { PrismaService } from '../prisma/prisma.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto } from './dto';
export declare class InputCategoriesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createInputCategoryDto: CreateInputCategoryDto, userId: string): Promise<any>;
    findAll(): Promise<any>;
    findTree(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateInputCategoryDto: UpdateInputCategoryDto, userId: string): Promise<any>;
    remove(id: number, userId: string): Promise<any>;
    private checkCircularReference;
}
