"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancingAgentResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class FinancingAgentResponseDto {
}
exports.FinancingAgentResponseDto = FinancingAgentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the financing agent',
        example: 1,
    }),
    __metadata("design:type", Number)
], FinancingAgentResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the financing agent',
        example: 'World Bank',
    }),
    __metadata("design:type", String)
], FinancingAgentResponseDto.prototype, "agentName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the financing agent',
        example: 'International financial institution providing loans and grants',
        required: false,
    }),
    __metadata("design:type", String)
], FinancingAgentResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Contact information for the financing agent',
        example: '<EMAIL>, +1-202-473-1000',
        required: false,
    }),
    __metadata("design:type", String)
], FinancingAgentResponseDto.prototype, "contactInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], FinancingAgentResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp',
        example: '2024-01-15T10:30:00Z',
    }),
    __metadata("design:type", Date)
], FinancingAgentResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=financing-agent-response.dto.js.map