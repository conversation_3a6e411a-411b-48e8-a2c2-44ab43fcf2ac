meta {
  name: PARTNER REGISTER
  type: http
  seq: 1
}

post {
  url: http://127.0.0.1:8080/api/v1/auth/register
  body: json
  auth: inherit
}

body:json {
  {
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "password": "SecureP@ssw0rd123",
    "organization": {
      "organizationName": "Global Health Alliance",
      "organizationRegistrationNumber": "GHA-2023-789",
      "organizationPhoneNumber": "+1-************",
      "organizationEmail": "<EMAIL>",
      "organizationWebsite": "https://www.gha.org",
      "homeCountryRepresentative": "<PERSON>",
      "rwandaRepresentative": "<PERSON> Umutoni",
      "organizationRgbNumber": "RGB123456789",
      "organizationTypeId": 2,
      "addresses": [
        {
          "addressType": "HEADQUARTERS",
          "country": "United States",
          "province": "California",
          "district": "Los Angeles County",
          "sector": "Westside",
          "cell": "Brentwood",
          "village": "Sunset Village",
          "street": "Wilshire Blvd",
          "avenue": "Avenue 5",
          "poBox": "PO Box 456",
          "postalCode": "90025"
        }
      ]
    }
  }
}
