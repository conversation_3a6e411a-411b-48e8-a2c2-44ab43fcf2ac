import { Injectable, ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateFinancingSchemeDto, UpdateFinancingSchemeDto } from './dto';

@Injectable()
export class FinancingSchemesService {
  constructor(private prisma: PrismaService) {}

  async create(createFinancingSchemeDto: CreateFinancingSchemeDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can create financing schemes');
    }

    // Check if scheme with same name already exists
    const existingScheme = await this.prisma.financingScheme.findUnique({
      where: { schemeName: createFinancingSchemeDto.schemeName },
    });

    if (existingScheme) {
      throw new ConflictException('Financing scheme with this name already exists');
    }

    return this.prisma.financingScheme.create({
      data: createFinancingSchemeDto,
    });
  }

  async findAll() {
    return this.prisma.financingScheme.findMany({
      where: { deleted: false },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: number) {
    const scheme = await this.prisma.financingScheme.findUnique({
      where: { id, deleted: false },
    });

    if (!scheme) {
      throw new NotFoundException('Financing scheme not found');
    }

    return scheme;
  }

  async update(id: number, updateFinancingSchemeDto: UpdateFinancingSchemeDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can update financing schemes');
    }

    // Check if scheme exists
    const existingScheme = await this.prisma.financingScheme.findUnique({
      where: { id, deleted: false },
    });

    if (!existingScheme) {
      throw new NotFoundException('Financing scheme not found');
    }

    // Check if new name conflicts with existing scheme (if name is being updated)
    if (updateFinancingSchemeDto.schemeName && updateFinancingSchemeDto.schemeName !== existingScheme.schemeName) {
      const conflictingScheme = await this.prisma.financingScheme.findUnique({
        where: { schemeName: updateFinancingSchemeDto.schemeName },
      });

      if (conflictingScheme) {
        throw new ConflictException('Financing scheme with this name already exists');
      }
    }

    return this.prisma.financingScheme.update({
      where: { id },
      data: updateFinancingSchemeDto,
    });
  }

  async remove(id: number, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can delete financing schemes');
    }

    // Check if scheme exists
    const existingScheme = await this.prisma.financingScheme.findUnique({
      where: { id, deleted: false },
    });

    if (!existingScheme) {
      throw new NotFoundException('Financing scheme not found');
    }

    // Soft delete
    return this.prisma.financingScheme.update({
      where: { id },
      data: { deleted: true },
    });
  }
}
