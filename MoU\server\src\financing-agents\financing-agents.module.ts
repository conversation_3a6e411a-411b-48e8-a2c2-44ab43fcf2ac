import { Module } from '@nestjs/common';
import { FinancingAgentsService } from './financing-agents.service';
import { FinancingAgentsController } from './financing-agents.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [FinancingAgentsController],
  providers: [FinancingAgentsService],
  exports: [FinancingAgentsService],
})
export class FinancingAgentsModule {}
