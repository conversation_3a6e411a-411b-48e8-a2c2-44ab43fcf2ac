import { Module } from '@nestjs/common';
import { InputCategoriesService } from './input-categories.service';
import { InputCategoriesController } from './input-categories.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [InputCategoriesController],
  providers: [InputCategoriesService],
  exports: [InputCategoriesService],
})
export class InputCategoriesModule {}
