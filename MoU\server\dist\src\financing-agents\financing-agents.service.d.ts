import { PrismaService } from '../prisma/prisma.service';
import { CreateFinancingAgentDto, UpdateFinancingAgentDto } from './dto';
export declare class FinancingAgentsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createFinancingAgentDto: CreateFinancingAgentDto, userId: string): Promise<any>;
    findAll(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateFinancingAgentDto: UpdateFinancingAgentDto, userId: string): Promise<any>;
    remove(id: number, userId: string): Promise<any>;
}
