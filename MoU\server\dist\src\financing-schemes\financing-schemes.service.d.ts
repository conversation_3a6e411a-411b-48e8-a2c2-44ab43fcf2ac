import { PrismaService } from '../prisma/prisma.service';
import { CreateFinancingSchemeDto, UpdateFinancingSchemeDto } from './dto';
export declare class FinancingSchemesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createFinancingSchemeDto: CreateFinancingSchemeDto, userId: string): Promise<any>;
    findAll(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateFinancingSchemeDto: UpdateFinancingSchemeDto, userId: string): Promise<any>;
    remove(id: number, userId: string): Promise<any>;
}
