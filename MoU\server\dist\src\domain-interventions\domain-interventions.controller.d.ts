import { DomainInterventionsService } from './domain-interventions.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';
export declare class DomainInterventionsController {
    private readonly domainInterventionsService;
    constructor(domainInterventionsService: DomainInterventionsService);
    create(createDomainInterventionDto: CreateDomainInterventionDto, req: any): Promise<any>;
    findAll(): Promise<any>;
    findTree(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, req: any): Promise<any>;
    remove(id: number, req: any): Promise<any>;
}
