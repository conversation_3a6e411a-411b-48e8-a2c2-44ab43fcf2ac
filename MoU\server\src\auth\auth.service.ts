import { BadRequestException, ConflictException, ForbiddenException, Injectable, InternalServerErrorException, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from 'src/prisma/prisma.service';
import { EmailService } from 'src/email/email.service';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { AcceptInvitationDto, ForgotPasswordDto, InviteUserDto, LoginDto, RefreshTokenDto, RegisterDto, ResetPasswordDto, VerifyEmailDto } from './dto';
import { User } from '@prisma/client';

@Injectable()
export class AuthService {
    private readonly logger = new Logger(AuthService.name);
    private readonly SALT_ROUNDS = 10;
    private readonly EMAIL_VERIFICATION_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours
    private readonly PASSWORD_RESET_EXPIRY = 60 * 60 * 1000; // 1 hour
    private readonly REFRESH_TOKEN_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days
    private readonly INVITATION_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days

    constructor(
        private jwtService: JwtService,
        private prisma: PrismaService,
        private emailService: EmailService,
        private configService: ConfigService
    ) { }

    /**
     * Generate access token for a user
     */
    async generateAccessToken(user: User) {
        const payload = {
            sub: user.id,
            email: user.email,
            role: user.role
        };
        return this.jwtService.sign(payload, {
            secret: this.configService.get<string>('JWT_SECRET'),
            expiresIn: '15m' // Short-lived access token
        });
    }

    /**
     * Generate refresh token for a user
     */
    async generateRefreshToken(user: User) {
        const refreshToken = uuidv4();

        try {
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    refreshToken
                }
            });

            return refreshToken;
        } catch (error) {
            this.logger.error(`Failed to generate refresh token for user ${user.id}`, error.stack);
            throw new InternalServerErrorException('Failed to generate refresh token');
        }
    }

    /**
     * Get user by ID
     */
    async getUser(id: string) {
        try {
            const user = await this.prisma.user.findUnique({ where: { id } })
            if (!user) {
                throw new NotFoundException('User not found');
            }
            return user;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to get user with ID ${id}`, error.stack);
            throw new InternalServerErrorException('Failed to get user');
        }
    }

    /**
     * Validate user credentials
     */
    async validateUser(email: string, password: string) {
        try {
            const user = await this.prisma.user.findUnique({ where: { email } });
            if (!user) {
                return null;
            }

            const isPasswordValid = await bcrypt.compare(password, user.password);
            if (!isPasswordValid) {
                return null;
            }

            return user;
        } catch (error) {
            this.logger.error(`Failed to validate user with email ${email}`, error.stack);
            return null;
        }
    }

    /**
     * Register a PARTNER user with organization creation (main registration endpoint)
     */
    async register(registerDto: RegisterDto) {
        const { firstName, lastName, email, password, organization } = registerDto;

        try {
            // Check if user already exists
            const exists = await this.prisma.user.findUnique({ where: { email } });
            if (exists) {
                throw new ConflictException('User with this email already exists');
            }

            // Validate organization data
            this.validateOrganizationData(organization);

            // Check if organization with same registration number or email already exists
            const existingOrg = await this.prisma.organization.findFirst({
                where: {
                    OR: [
                        { organizationRegistrationNumber: organization.organizationRegistrationNumber },
                        { organizationEmail: organization.organizationEmail }
                    ],
                    deleted: false
                }
            });

            if (existingOrg) {
                throw new ConflictException('Organization with this registration number or email already exists');
            }

            // Check if organization type exists
            const orgType = await this.prisma.organizationType.findUnique({
                where: { id: organization.organizationTypeId }
            });

            if (!orgType) {
                throw new NotFoundException('Organization type not found');
            }

            // Generate email verification token
            const verificationToken = uuidv4();
            const verificationTokenExpiryTime = new Date(Date.now() + this.EMAIL_VERIFICATION_EXPIRY);

            // Hash password
            const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS);

            // Create organization and user in transaction
            const result = await this.prisma.$transaction(async (prisma) => {
                // Create organization
                const newOrg = await prisma.organization.create({
                    data: {
                        organizationName: organization.organizationName,
                        organizationRegistrationNumber: organization.organizationRegistrationNumber,
                        organizationPhoneNumber: organization.organizationPhoneNumber,
                        organizationEmail: organization.organizationEmail,
                        organizationWebsite: organization.organizationWebsite,
                        homeCountryRepresentative: organization.homeCountryRepresentative,
                        rwandaRepresentative: organization.rwandaRepresentative,
                        organizationRgbNumber: organization.organizationRgbNumber,
                        organizationTypeId: organization.organizationTypeId
                    }
                });

                // Create addresses for the organization
                for (const addressData of organization.addresses) {
                    await prisma.address.create({
                        data: {
                            ...addressData,
                            organizationId: newOrg.id
                        }
                    });
                }

                // Create the PARTNER user
                const newUser = await prisma.user.create({
                    data: {
                        firstName,
                        lastName,
                        email,
                        password: hashedPassword,
                        role: 'PARTNER',
                        organizationId: newOrg.id,
                        verificationToken,
                        verificationTokenExpiryTime,
                        emailVerified: false
                    },
                });

                // Send verification email (not welcome email)
                await this.emailService.sendEmailVerificationEmail({
                    email: newUser.email,
                    firstName: newUser.firstName,
                    lastName: newUser.lastName,
                    verificationToken: verificationToken
                });

                return { user: newUser, organization: newOrg };
            });

            // Generate tokens
            const accessToken = await this.generateAccessToken(result.user);
            const refreshToken = await this.generateRefreshToken(result.user);

            return {
                user: {
                    id: result.user.id,
                    email: result.user.email,
                    firstName: result.user.firstName,
                    lastName: result.user.lastName,
                    role: result.user.role,
                    emailVerified: result.user.emailVerified,
                    organizationId: result.user.organizationId
                },
                organization: {
                    id: result.organization.id,
                    organizationName: result.organization.organizationName,
                    organizationEmail: result.organization.organizationEmail
                },
                accessToken,
                refreshToken
            };
        } catch (error) {
            if (error instanceof ConflictException || error instanceof NotFoundException ||
                error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to register partner with organization for email ${email}`, error.stack);
            throw new InternalServerErrorException('Failed to register partner with organization');
        }
    }

    /**
     * Create user by admin (for non-PARTNER roles)
     */
    async createUserByAdmin(createUserDto: any, adminUserId: string) {
        const { firstName, lastName, email, role, organizationId } = createUserDto;

        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: adminUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create users
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create users');
            }

            // Check if user already exists
            const exists = await this.prisma.user.findUnique({ where: { email } });
            if (exists) {
                throw new ConflictException('User with this email already exists');
            }

            // Business rule validation
            if (role === 'PARTNER') {
                throw new ForbiddenException('ADMIN users cannot create PARTNER users. PARTNER users must self-register with organization data.');
            }

            if (role === 'ADMIN' && organizationId) {
                throw new BadRequestException('ADMIN users cannot be associated with an organization');
            }

            // Check if organization exists (if provided)
            if (organizationId) {
                const organization = await this.prisma.organization.findUnique({
                    where: { id: organizationId }
                });

                if (!organization) {
                    throw new NotFoundException('Organization not found');
                }
            }

            // Generate temporary password
            const tempPassword = uuidv4().substring(0, 12);
            const hashedPassword = await bcrypt.hash(tempPassword, this.SALT_ROUNDS);

            // Generate verification token
            const verificationToken = uuidv4();
            const verificationTokenExpiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

            const user = await this.prisma.user.create({
                data: {
                    firstName,
                    lastName,
                    email,
                    password: hashedPassword,
                    role: role,
                    organizationId: organizationId || null,
                    verificationToken,
                    verificationTokenExpiryTime,
                    emailVerified: false
                },
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                }
            });

            // Send verification email with temporary password
            await this.emailService.sendEmailVerificationEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                verificationToken: verificationToken,
                tempPassword: tempPassword
            });

            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
                tempPassword // Return temp password for admin to share with user
            };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException ||
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to create user for email ${email}`, error.stack);
            throw new InternalServerErrorException('Failed to create user');
        }
    }

    private validateOrganizationData(organization: any) {
        // Validate addresses
        if (!organization.addresses || organization.addresses.length === 0) {
            throw new BadRequestException('Organization must have at least one address (either Rwanda or headquarters)');
        }

        if (organization.addresses.length > 2) {
            throw new BadRequestException('Organization cannot have more than 2 addresses');
        }

        // Check for duplicate address types
        const addressTypes = organization.addresses.map((addr: any) => addr.addressType);
        const uniqueTypes = new Set(addressTypes);
        if (addressTypes.length !== uniqueTypes.size) {
            throw new BadRequestException('Cannot have duplicate address types');
        }

        // Validate address type constraints
        const validAddressTypes = ['RWANDA', 'HEADQUARTERS'];
        for (const address of organization.addresses) {
            if (!validAddressTypes.includes(address.addressType)) {
                throw new BadRequestException('Invalid address type. Must be either RWANDA or HEADQUARTERS');
            }
        }

        // Validate Rwanda address fields if Rwanda address is provided
        const rwandaAddress = organization.addresses.find((addr: any) => addr.addressType === 'RWANDA');
        if (rwandaAddress && (!rwandaAddress.province || !rwandaAddress.district)) {
            throw new BadRequestException('Rwanda address must include province and district');
        }

        // Validate that at least one address is provided (this is already checked above, but keeping for clarity)
        const hasRwandaAddress = organization.addresses.some((addr: any) => addr.addressType === 'RWANDA');
        const hasHeadquartersAddress = organization.addresses.some((addr: any) => addr.addressType === 'HEADQUARTERS');

        if (!hasRwandaAddress && !hasHeadquartersAddress) {
            throw new BadRequestException('Organization must have at least one address (either Rwanda or headquarters)');
        }
    }

    /**
     * Login a user
     */
    async login(loginDto: LoginDto) {
        const { email, password } = loginDto;

        try {
            // Find user
            const user = await this.prisma.user.findUnique({ where: { email } });
            if (!user) {
                throw new UnauthorizedException('Invalid credentials');
            }

            // Validate password
            const isValid = await bcrypt.compare(password, user.password);
            if (!isValid) {
                throw new UnauthorizedException('Invalid credentials');
            }

            // Generate tokens
            const accessToken = await this.generateAccessToken(user);
            const refreshToken = await this.generateRefreshToken(user);

            return {
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    emailVerified: user.emailVerified
                },
                accessToken,
                refreshToken
            };
        } catch (error) {
            if (error instanceof UnauthorizedException) {
                throw error;
            }
            this.logger.error(`Failed to login user with email ${email}`, error.stack);
            throw new InternalServerErrorException('Failed to login');
        }
    }

    /**
     * Refresh access token using refresh token
     */
    async refreshToken(refreshTokenDto: RefreshTokenDto) {
        const { refreshToken } = refreshTokenDto;

        try {
            // Find user with this refresh token
            const user = await this.prisma.user.findFirst({
                where: {
                    refreshToken
                }
            });

            if (!user) {
                throw new UnauthorizedException('Invalid refresh token');
            }

            // Generate new tokens
            const accessToken = await this.generateAccessToken(user);
            const newRefreshToken = await this.generateRefreshToken(user);

            return {
                accessToken,
                refreshToken: newRefreshToken
            };
        } catch (error) {
            if (error instanceof UnauthorizedException) {
                throw error;
            }
            this.logger.error('Failed to refresh token', error.stack);
            throw new InternalServerErrorException('Failed to refresh token');
        }
    }

    /**
     * Verify email with token
     */
    async verifyEmail(verifyEmailDto: VerifyEmailDto) {
        const { token } = verifyEmailDto;

        try {
            // Find user with this verification token
            const user = await this.prisma.user.findFirst({
                where: {
                    verificationToken: token,
                    verificationTokenExpiryTime: { gt: new Date() }
                }
            });

            if (!user) {
                throw new BadRequestException('Invalid or expired verification token');
            }

            // Update user to mark email as verified
            const updatedUser = await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    emailVerified: true,
                    verificationToken: null,
                    verificationTokenExpiryTime: null,
                    verifiedAt: new Date()
                }
            });

            // Send welcome email after successful verification
            await this.emailService.sendWelcomeEmail({
                email: updatedUser.email,
                firstName: updatedUser.firstName,
                lastName: updatedUser.lastName
            });

            return { message: 'Email verified successfully. Welcome email sent!' };
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to verify email with token ${token}`, error.stack);
            throw new InternalServerErrorException('Failed to verify email');
        }
    }

    /**
     * Request password reset
     */
    async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
        const { email } = forgotPasswordDto;

        try {
            // Find user
            const user = await this.prisma.user.findUnique({ where: { email } });
            if (!user) {
                // Don't reveal that the user doesn't exist for security reasons
                return { message: 'If your email is registered, you will receive a password reset link' };
            }

            // Generate password reset token
            const passwordResetToken = uuidv4();
            const passwordResetExpires = new Date(Date.now() + this.PASSWORD_RESET_EXPIRY);

            // Update user with reset token
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    passwordResetToken,
                    passwordResetExpires
                }
            });

            // Send password reset email
            await this.emailService.sendPasswordResetEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                resetToken: passwordResetToken
            });

            return { message: 'If your email is registered, you will receive a password reset link' };
        } catch (error) {
            this.logger.error(`Failed to process forgot password for email ${email}`, error.stack);
            // Don't reveal errors for security reasons
            return { message: 'If your email is registered, you will receive a password reset link' };
        }
    }

    /**
     * Reset password with token
     */
    async resetPassword(resetPasswordDto: ResetPasswordDto) {
        const { token, password } = resetPasswordDto;

        try {
            // Find user with this reset token
            const user = await this.prisma.user.findFirst({
                where: {
                    passwordResetToken: token,
                    passwordResetExpires: { gt: new Date() }
                }
            });

            if (!user) {
                throw new BadRequestException('Invalid or expired reset token');
            }

            // Hash new password
            const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS);

            // Update user with new password
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    password: hashedPassword,
                    passwordResetToken: null,
                    passwordResetExpires: null
                }
            });

            return { message: 'Password reset successfully' };
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to reset password with token ${token}`, error.stack);
            throw new InternalServerErrorException('Failed to reset password');
        }
    }

    /**
     * Invite a user to join an organization (Admin only)
     */
    async inviteUser(inviteUserDto: InviteUserDto, inviterId: string) {
        const { email, role, organizationId } = inviteUserDto;

        try {
            // Check if inviter exists and has permission
            const inviter = await this.prisma.user.findUnique({
                where: { id: inviterId },
                include: { organization: true }
            });

            if (!inviter) {
                throw new NotFoundException('Inviter not found');
            }

            // Only ADMIN can invite users
            const inviterRole = String(inviter.role);
            if (inviterRole !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can invite users');
            }

            // Check if organization exists (if provided)
            if (organizationId) {
                const organization = await this.prisma.organization.findUnique({ where: { id: organizationId } });
                if (!organization) {
                    throw new NotFoundException(`Organization with ID ${organizationId} not found`);
                }
            }

            // Check if user already exists
            const existingUser = await this.prisma.user.findUnique({ where: { email } });
            if (existingUser) {
                throw new ConflictException('User with this email already exists');
            }

            // Generate invitation token
            const invitationToken = uuidv4();
            const invitationExpires = new Date(Date.now() + this.INVITATION_EXPIRY);

            // Create invitation (placeholder user)
            await this.prisma.$transaction(async (prisma) => {
                // Create placeholder user with invitation token
                const placeholderUser = await prisma.user.create({
                    data: {
                        email,
                        firstName: 'Invited',
                        lastName: 'User',
                        password: await bcrypt.hash(uuidv4(), this.SALT_ROUNDS), // Random password
                        role: role as any,
                        organizationId: organizationId ?? null,
                        invitationToken,
                        invitationExpires,
                        invitedBy: inviterId,
                        emailVerified: false
                    }
                });

                // Send invitation email
                await this.emailService.sendInvitationEmail({
                    email,
                    invitationToken,
                    inviterName: `${inviter.firstName} ${inviter.lastName}`,
                    companyName: inviter.organization?.organizationName || 'Ministry of Health',
                    companyDescription: `Join the Ministry of Health MoU Management System as a ${role.toLowerCase()}`,
                    role: role.toLowerCase()
                });

                return placeholderUser;
            });

            return { message: `Invitation sent to ${email}` };
        } catch (error) {
            if (error instanceof NotFoundException ||
                error instanceof ForbiddenException ||
                error instanceof ConflictException) {
                throw error;
            }
            this.logger.error(`Failed to invite user with email ${email}`, error.stack);
            throw new InternalServerErrorException('Failed to send invitation');
        }
    }

    /**
     * Accept an invitation
     */
    async acceptInvitation(acceptInvitationDto: AcceptInvitationDto) {
        const { token, firstName, lastName, password } = acceptInvitationDto;

        try {
            // Find invitation
            const invitation = await this.prisma.user.findFirst({
                where: {
                    invitationToken: token,
                    invitationExpires: { gt: new Date() }
                },
                include: { organization: true }
            });

            if (!invitation) {
                throw new BadRequestException('Invalid or expired invitation');
            }

            // Hash password
            const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS);

            // Update user with provided information
            const user = await this.prisma.user.update({
                where: { id: invitation.id },
                data: {
                    firstName,
                    lastName,
                    password: hashedPassword,
                    invitationToken: null,
                    invitationExpires: null,
                    emailVerified: true, // Auto-verify email for invited users
                    verifiedAt: new Date()
                }
            });

            // Send welcome email
            await this.emailService.sendWelcomeEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName
            });

            // Generate tokens
            const accessToken = await this.generateAccessToken(user);
            const refreshToken = await this.generateRefreshToken(user);

            return {
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    emailVerified: user.emailVerified,
                    organization: invitation.organization ? {
                        id: invitation.organization.id,
                        name: invitation.organization.organizationName
                    } : null
                },
                accessToken,
                refreshToken
            };
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to accept invitation with token ${token}`, error.stack);
            throw new InternalServerErrorException('Failed to accept invitation');
        }
    }

    /**
     * Resend verification email
     */
    async resendVerificationEmail(userId: string) {
        try {
            const user = await this.prisma.user.findUnique({ where: { id: userId } });
            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.emailVerified) {
                throw new BadRequestException('Email is already verified');
            }

            // Generate new verification token
            const verificationToken = uuidv4();
            const verificationTokenExpiryTime = new Date(Date.now() + this.EMAIL_VERIFICATION_EXPIRY);

            // Update user with new token
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    verificationToken,
                    verificationTokenExpiryTime
                }
            });

            // Send verification email
            await this.emailService.sendEmailVerificationEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                verificationToken: verificationToken
            });

            return { message: 'Verification email sent' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to resend verification email for user ${userId}`, error.stack);
            throw new InternalServerErrorException('Failed to resend verification email');
        }
    }

    /**
     * Logout a user
     */
    async logout(userId: string) {
        try {
            await this.prisma.user.update({
                where: { id: userId },
                data: {
                    refreshToken: null
                }
            });

            return { message: 'Logged out successfully' };
        } catch (error) {
            this.logger.error(`Failed to logout user ${userId}`, error.stack);
            throw new InternalServerErrorException('Failed to logout');
        }
    }
}
