import { FinancingAgentsService } from './financing-agents.service';
import { CreateFinancingAgentDto, UpdateFinancingAgentDto } from './dto';
export declare class FinancingAgentsController {
    private readonly financingAgentsService;
    constructor(financingAgentsService: FinancingAgentsService);
    create(createFinancingAgentDto: CreateFinancingAgentDto, req: any): Promise<any>;
    findAll(): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateFinancingAgentDto: UpdateFinancingAgentDto, req: any): Promise<any>;
    remove(id: number, req: any): Promise<any>;
}
